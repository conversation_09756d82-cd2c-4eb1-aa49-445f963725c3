import https from 'node:https';

export default async function checkCVSToken(token, brand, country, source, os) {
  var options = { timeout: 10000, headers: {token}, query: {brand}};
  const url = process.env.SP_PROC_ME_URL+"/v1/introspection?brand="+brand+"&country="+country+"&source="+source+"&os="+os;
  let res = await fetch(url, options)
  console.log("calling "+url+" results: "+res)
  res= JSON.parse(res)
  
  return  res.success?.userId
}

async function fetch(url, options) {
   return new Promise((resolve, reject) => {
    const request = https.get(url, options, (res) => {
      if (res.statusCode < 200 || res.statusCode > 299) {
        console.log(`HTTP Error url : ${url} statusCode :${res.statusCode}`)
      }
      const body = []
      res.on('data', (chunk) => body.push(chunk))
      res.on('end', () => {
        const resString = Buffer.concat(body).toString()
        resolve(resString)
      })
    })

    /*request.on('error', (err) => {
      reject(err)
    })*/
    request.on('timeout', () => {
      request.destroy()
      reject(new Error('timed out'))
    })
  })
}
