<?php

declare(strict_types=1);

namespace App\Manager;

use App\Exception\VehicleNotFoundException;
use App\Helper\ResponseArrayFormat;
use App\Helper\SecuredAccessor;
use App\Helper\SuccessResponse;
use App\Manager\Visual3DManager;
use App\Service\CorvetService;
use App\Service\LabelVehiculeService;
use App\Trait\LoggerTrait;

class VehicleManager
{
    use LoggerTrait;

    public function __construct(
        private CorvetService $corvetService,
        private LabelVehiculeService $lvhService,
        private Visual3DManager $visual3DManager
    ) {
    }

    public function getVehicleData(
        string $brand,
        string $country,
        string $language,
        string $source,
        string $vin,
        bool $isfullDetail
    ): array
    {

        $vehicle = $this->loadVehicleFromCorvet(
            $brand,
            $country,
            $language,
            $source,
            $vin,
            $isfullDetail
        );

        if (!$vehicle) {
            return [];
        }

        $lcdv = '';
        $lcdv = SecuredAccessor::getSecuredFromArray(
            $vehicle,
            'lcdv',
            ''
        );
        if (!$isfullDetail) {
            $vehicle = SecuredAccessor::removeKeysIfExist($vehicle, 'lcdv');
        }
        $vehicle = SecuredAccessor::removeKeysIfExist($vehicle, 'options');

        return $vehicle;
    }

    public function mapSearchApiResultsAsXPFormat(array $data, bool $isFullDetail): array
    {
        $vehicle = [
            'vin' => $data['vin'],
            'lcdv' => $data['lcdv'] ?? null,
            'short_label' => $data['label'] ?? null,
            'warranty_start_date' => $data['warrantyStartDate'] ?? null,
            'type_vehicle' => isset($data['type_vehicle']) ? (int) $data['type_vehicle'] : null, 
            'elegibility' => null, 
            'attributes' => $data['attributes'] ?? null, 
            'types' => $data['types'] ?? null, 
            'visual' => $data['visual'],
            'isO2x' => $data['isO2x'] ?? null,
        ];

        return $vehicle;
    }

    public function mapSearchApiResults(array $data, bool $isFullDetail): array
    {
        $vehicle = [
            'vin' => $data['vin'],
            'label' => $data['label'],
            'visual' => $data['picture'],
            'isO2x' => $data['isO2x'] ?? false,
        ];

        if (true === $isFullDetail) {
            $vehicle = array_merge($vehicle, [
                'lcdv' => $data['lcdv'],
                'warrantyStartDate' => $data['warrantyStartDate'],
            ]);
        }

        return $vehicle;
    }

    /**
     * Get Vehicle based on the given parameters.
     *
     * @param array $parameters
     */
    public function getVehicle(array $parameters): ResponseArrayFormat
    {
        $brand = $parameters['brand'];
        $vin = $parameters['vin'];
        $country = $parameters['country'];
        $language = $parameters['language'];
        $source = strtoupper($parameters['source']);
        $isfullDetail = $parameters['isfullDetail'];

        $vehicle = $this->getVehicleData(
            $brand,
            $country,
            $language,
            $source,
            $vin,
            $isfullDetail
        );
        if (!$vehicle) {
            throw VehicleNotFoundException::make();
        }

        // Map the search API results
        /** @scrutinizer ignore-call */ 
        $vehicle = $this->mapSearchApiResultsAsXPFormat($vehicle, $isfullDetail);
        $vehicle = $this->removeNullValues($vehicle);

        return (new SuccessResponse())->setData($vehicle);
    }

    

    public function loadVehicleFromCorvet(
        string $brand,
        string $country,
        string $language,
        string $source,
        string $vin,
        bool $isfullDetail = true
    ) {
        // now forced to support XP output format
        $isfullDetail = true;

        $corvetResponse = $this->corvetService->getLcdv(
            $brand,
            $country,
            $vin,
            $source,
            true
        );

        $lcdv = $corvetResponse['data'] ?? null;
        $vehicleOptions = $corvetResponse['options'] ?? [];

        if (!$lcdv) {
            return [];
        }

        $warrantyStartDate = \DateTime::createFromFormat('d/m/Y H:i:s', $corvetResponse['warranty_start_date'] ?? '');
        $warranty = $warrantyStartDate ? (int) $warrantyStartDate->format('U') : null;

        /** @scrutinizer ignore-call */ 
        $vehicleLabelInfo = $this->getVehicleLabelInfo(
            $brand,
            $country,
            $language,
            $lcdv
        );

        $response = [
            'vin' => $vin,
            'label' => $vehicleLabelInfo['label'],
            'isO2x' => $vehicleLabelInfo['isO2x'],
            'options' => $vehicleOptions,
            'lcdv' => $lcdv,
            'visual' => $vehicleLabelInfo['picture'],
            'type_vehicle' => $corvetResponse['type_vehicle'] ?? null,
            'attributes' => $corvetResponse['attributes'] ?? null,
            'types' => $corvetResponse['types'] ?? null,
        ];

        if (true === $isfullDetail) {
            $response = array_merge($response, [
                'warrantyStartDate' => $warranty,
            ]);
        }

        return $response;
    }

    private function getVisual(
        string $brand,
        ?string $source = 'APP',
        ?string $lcdvOrRpo = '',
        ?string $vin = null,
        ?array $vehicleOptions = []
    ): ?string
    {

        $visualResponse = $this->visual3DManager->loadImages(
            $lcdvOrRpo,
            $brand,
            $source,
            $vehicleOptions
        );

        return $visualResponse['data'] ?? null;
    }

    private function getVehicleLabelInfo(
        string $brand,
        string $country,
        string $language,
        string $lcdv = null
    ): array {

        $response = $this->lvhService->getLvh(
            $brand,
            $country,
            $language,
            $lcdv
        );
        $lvh = $response->getArrayFormat()['success'];

        $data = [];
        $data['label'] = isset($lvh['label']) ? $lvh['label'] : null;
        $data['isO2x'] = isset($lvh['isO2x']) ? $lvh['isO2x'] : false;
        $data['picture'] = isset($lvh['defaultImage']) ? $lvh['defaultImage'] : null;
        return $data;
    }

    public function removeNullValues(array $array): array 
    {
        foreach($array as $key => $value) {
            if(is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            }
            if(is_null($array[$key])) {
                unset($array[$key]);
            }
        }        
        return $array;
    }
}
