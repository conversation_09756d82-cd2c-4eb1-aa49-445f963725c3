<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Kind of HttpClientInterface decorator.
 */
class CustomHttpClient
{
    use LoggerTrait;

    public function __construct(private HttpClientInterface $client)
    {
    }

    public function request(string $method, string $url, array $options = []): WSResponse
    {
        try {
            $this->logger->info("Call URL: {$url} {$method}");
            $response = $this->client->request($method, $url, $options);
            $statusCode = $response->getStatusCode();
            $data = Response::HTTP_NO_CONTENT != $statusCode ? $response->toArray(false) : [];
            $response = new WSResponse($statusCode, $data);

            return $response;
        } catch (\Exception $e) {
            $this->logger->error('Cached Exception : CustomHttpClient::request '.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
