<?php

namespace App\Controller;

use App\Manager\ChargingStationManagementManager;
use App\Manager\UserManager;
use App\Model\ShowRoomEligibilityList;
use App\Model\Wallet;
use App\Trait\ValidationResponseTrait;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Trait\LoggerTrait;

#[Route('v1/charging-station', name: 'charging_station_management_')]
class ChargingStationManagementController extends AbstractController
{
    use ValidationResponseTrait;
    use LoggerTrait;

    #[OA\Tag(name: 'Charging History')]
    #[Route('/history', name: 'history', methods: [Request::METHOD_GET])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'cpo',
        in: 'query',
        description: 'CPO',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'from',
        in: 'query',
        description: 'FROM (timestimp)',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'to',
        in: 'query',
        description: 'TO (timestimp)',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'status',
        in: 'query',
        description: 'STATUS',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'url'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Access denied due to invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    public function history(
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator,
    ): Response {
        $userId = $request->headers->get('userId', '');
        $cpo = $request->query->get('cpo', '');
        $from = $request->query->get('from', '');
        $to = $request->query->get('to', '');
        $status = $request->query->get('status', '');
        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }

        $params = [
            'cpo' => $cpo,
            'from' => $from,
            'to' => $to,
            'status' => $status,
        ];

        $response = $chargingStationManagementManager->getHistory($params, $userId)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Ishowroom Credit')]
    #[Route('/ishowroom/eligibility', name: 'ishowroom_eligibility', methods: [Request::METHOD_POST])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        required: true,
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'email', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new Model(type: ShowRoomEligibilityList::class)
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object'),
                ]),
            ]
        )
    )]
    public function ishowroomEligibility(
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator,
    ): Response {
        $userId = $request->headers->get('userId', '');
        $content = $request->toArray();
        $email = $content['email'] ?? '';
        $errors = $validator->validate(
            compact('userId', 'email'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'email' => [
                    new Assert\NotBlank(),
                    new Assert\Email()
                ]
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $chargingStationManagementManager->getIshowroomEligibility($userId, $email)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Wallet')]
    #[Route('/wallet', name: 'wallet', methods: [Request::METHOD_GET])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'object',
                    ref: new Model(type: Wallet::class)
                ),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNAUTHORIZED,
        description: 'Invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    public function getWalletDetail(
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator,
    ): Response {
        $userId = $request->headers->get('userId', '');
        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $chargingStationManagementManager->getWalletDetail($userId)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/user/enrolment', name: 'enrolment', methods: ['POST'])]
    #[OA\Tag(name: 'Enrolment')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        required: true,
        content: new JsonContent(
            type: 'object',
            properties: [
                /*new OA\Property(property: 'firstName', type: 'string'),
                new OA\Property(property: 'lastName', type: 'string'),
                new OA\Property(property: 'email', type: 'string'),
                new OA\Property(property: 'zipCode', type: 'integer'),
                new OA\Property(property: 'country', type: 'string'),
                new OA\Property(property: 'countryCode', type: 'string'),
                new OA\Property(property: 'state', type: 'string'),
                new OA\Property(property: 'city', type: 'string'),*/
                new OA\Property(property: 'vin', type: 'string'),
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'enrolmentStatus', type: 'string', default: 'complete'),
                        new OA\Property(property: 'payementMethodUrl', type: 'string'),
                        new OA\Property(property: 'accountLinkingUrl', type: 'string'),
                        new OA\Property(property: 'accountLinkingRedirectUrl', type: 'string')
                    ]
                ),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'enrolmentStatus', default: 'missingProfileData')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    public function register(Request $request, UserManager $userManager, ValidatorInterface $validator): Response
    {
        $userId = $request->headers->get('userId');
        $params = $request->toArray();

        $constraints = new Assert\Collection([
            'vin' => new Assert\Optional([new Assert\Length(['min' => 17, 'max' => 17])]),
            'userId' => new Assert\NotBlank()
        ]);

        $errors = $validator->validate(array_merge($params, ['userId'=> $userId]), $constraints);
        $messages = static::getValidationMessages($errors);

        if (!empty($messages)) {
            return $this->jsonResponse($this->getValidationErrorResponse($messages));
        }

        return $this->jsonResponse($userManager->register($params, $userId));
    }

    #[OA\Tag(name: 'Session')]
    #[Route('/session/{id}/stop', name: 'session_stop', methods: [Request::METHOD_POST])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', type: 'string', default: 'Session stoped successfully')
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNAUTHORIZED,
        description: 'Invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    public function sessionStop(
        string $id,
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator,
    ): Response {
        $userId = $request->headers->get('userId', '');
        $errors = $validator->validate(
            compact('userId', 'id'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'id' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $chargingStationManagementManager->stopSession($userId, $id)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Session')]
    #[Route('/session/start', name: 'session_start', methods: [Request::METHOD_POST])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'vin',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'locationId',
        in: 'header',
        description: 'location Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'evseId',
        in: 'header',
        description: 'evse Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'lat',
        in: 'header',
        description: 'latitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'long',
        in: 'header',
        description: 'longitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'connectorId',
        in: 'header',
        description: 'connector Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', type: 'string', default: 'Session started successfully')
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNAUTHORIZED,
        description: 'Invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    public function sessionStart(
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator
    ): Response {
        $userId = $request->headers->get('userId', '');
        $vin = $request->headers->get('vin', '');
        $locationId = $this->cleanHeaderValue($request->headers->get('locationId', ''));
        $evseId = $this->cleanHeaderValue($request->headers->get('evseId', ''));
        $longitude = $this->cleanHeaderValue($request->headers->get('long', ''));
        $latitude = $this->cleanHeaderValue($request->headers->get('lat', ''));
        $connectorId = $this->cleanHeaderValue($request->headers->get('connectorId', ''));

        $this->logger->info('START_SESSION:: Request data:', [
            'headers' => $request->headers->all(),
            'method' => $request->getMethod(),
            'uri' => $request->getRequestUri(),
            'content' => $request->getContent()
        ]);

        $errors = $validator->validate(
            compact('userId', 'vin', 'locationId', 'evseId', 'longitude', 'latitude', 'connectorId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => new Assert\NotBlank(),
                'locationId' => new Assert\NotBlank(),
                'evseId' => new Assert\NotBlank(),
                'longitude' => new Assert\NotBlank(),
                'latitude' => new Assert\NotBlank(),
                'connectorId' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);

        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $payload = [
            'locationId' => $locationId,
            'evseId' => $evseId,
            'connectorId' => $connectorId,
            'long' => $longitude,
            'lat' => $latitude
        ];

        $this->logger->info(__CLASS__ . " : " . __METHOD__ . " for payload: ", ['payload' => $payload]);

        $response = $chargingStationManagementManager->startSession($userId, $vin, $payload)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    private function jsonResponse($response): Response
    {
        $formattedResponse = $response->getArrayFormat();

        return $this->json($formattedResponse['content'], $formattedResponse['code']);
    }

    #[OA\Tag(name: 'Ishowroom Credit')]
    #[Route('/ishowroom/credit', name: 'apply_ishowroom_credit', methods: [Request::METHOD_POST])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        required: true,
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'id', type: 'string'),
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The credit applied successfully.'),
                        new OA\Property(property: 'name', type: 'string', example: 'APPLY_CREDIT_SUCCESS'),
                    ]
                ),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 409,
        description: 'Conflict error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    public function applyIshowroomCredit(Request $request, ChargingStationManagementManager $chargingStationManagementManager, ValidatorInterface $validator): JsonResponse
    {
        try {
            $userId = $request->headers->get('userId', '');
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? '';
            $errors = $validator->validate(compact('id', 'userId'), [
                new Assert\Collection([
                    'id' => new Assert\NotBlank(),
                    'userId' => new Assert\NotBlank()
                ])
            ]);
            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

                return $this->json($response['content'], $response['code']);
            }
            $response = $chargingStationManagementManager->applyIshowroomCredit($userId, $id)->getArrayFormat();
            $response = $this->removeNullValues($response);

            return $this->json($response['content'], $response['code']);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[OA\Tag(name: 'Get Session')]
    #[Route('/session/{sessionId}', name: 'get session', methods: [Request::METHOD_GET])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'gigya user id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'userId', type: 'integer'),
                new OA\Property(property: 'locationId', type: 'string'),
                new OA\Property(property: 'evseId', type: 'string'),
                new OA\Property(property: 'powerType', type: 'string'),
                new OA\Property(property: 'cpoId', type: 'string'),
                new OA\Property(property: 'fixedCommission', type: 'string'),
                new OA\Property(property: 'commissionPercentage', type: 'string'),
                new OA\Property(property: 'cost', type: 'string'),
                new OA\Property(property: 'energy', type: 'string'),
                new OA\Property(property: 'status', type: 'string'),
                new OA\Property(property: 'startedAt', type: 'string'),
                new OA\Property(property: 'chargingCompletedAt', type: 'string'),
                new OA\Property(property: 'createdAt', type: 'string'),
                new OA\Property(property: 'updatedAt', type: 'string'),
                new OA\Property(property: 'vehicleMake', type: 'string'),
                new OA\Property(property: 'vehicleModel', type: 'string'),
                new OA\Property(property: 'vehicleModelYear', type: 'string'),
                new OA\Property(property: 'mpge', type: 'string'),
                new OA\Property(property: 'milesAdded', type: 'string'),
                new OA\Property(property: 'priceText', type: 'string'),
                new OA\Property(property: 'power', type: 'string'),
                new OA\Property(property: 'deltaPower', type: 'string'),
                new OA\Property(property: 'ocpiSessionId', type: 'string'),
                new OA\Property(property: 'ocpiLastUpdatedAt', type: 'string'),
                new OA\Property(property: 'parkingFee', type: 'string'),
                new OA\Property(property: 'offerTransactionId', type: 'string'),
                new OA\Property(property: 'sources', type: 'string'),
                new OA\Property(
                    property: 'coordinate',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'type', type: 'string'),
                        new OA\Property(
                            property: 'coordinates',
                            type: 'array',
                            items: new OA\Items(type: 'string')
                        )
                    ]
                ),
                new OA\Property(property: 'connectorId', type: 'string'),
                new OA\Property(property: 'connectorRefId', type: 'string'),
                new OA\Property(property: 'timeZone', type: 'string'),
                new OA\Property(property: 'currency', type: 'string'),
                new OA\Property(property: 'totalTime', type: 'string'),
                new OA\Property(property: 'providerShortCode', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    public function getSession(
        string $sessionId,
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator
    ): Response {
        $userId = $request->headers->get('userId', '');
        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $chargingStationManagementManager->getSession($userId, $sessionId)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'CDR Data')]
    #[Route('/session/{id}/cdr', name: 'cdr_data', methods: [Request::METHOD_POST])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'fixedCommission', type: 'string'),
                new OA\Property(property: 'commissionPercentage', type: 'string'),
                new OA\Property(property: 'cost', type: 'string'),
                new OA\Property(property: 'energy', type: 'string'),
                new OA\Property(property: 'status', type: 'string'),
                new OA\Property(property: 'startedAt', type: 'string'),
                new OA\Property(property: 'chargingCompletedAt', type: 'string'),
                new OA\Property(property: 'createdAt', type: 'string'),
                new OA\Property(property: 'updatedAt', type: 'string'),
                new OA\Property(property: 'connectonId', type: 'string'),
                new OA\Property(property: 'timeZone', type: 'string'),
                new OA\Property(property: 'currency', type: 'string'),
                new OA\Property(property: 'totalTime', type: 'integer'),
                new OA\Property(property: 'unitTime', type: 'string', default: 'ms'),
                new OA\Property(property: 'priceText', type: 'string'),
                new OA\Property(property: 'power', type: 'float'),
                new OA\Property(property: 'deltaPower', type: 'float'),
                new OA\Property(property: 'name', type: 'string'),
                new OA\Property(property: 'locationId', type: 'string'),
                new OA\Property(property: 'evseId', type: 'string'),
                new OA\Property(property: 'type', type: 'string'),
                new OA\Property(property: 'powerLevel', type: 'string'),
                new OA\Property(
                    property: 'position',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'latitude', type: 'float'),
                        new OA\Property(property: 'longitude', type: 'float'),
                        
                    ]
                ),
                new OA\Property(
                    property: 'address',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'streetNumber', type: 'string'),
                        new OA\Property(property: 'streetName', type: 'string'),
                        new OA\Property(property: 'municipality', type: 'string'),
                        new OA\Property(property: 'postalCode', type: 'string'),
                        new OA\Property(property: 'country', type: 'string'),
                        new OA\Property(property: 'countryCode', type: 'string'),
                        
                    ]
                )
                    ]
                ),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Token not found',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    public function getCdrData(
        string $id,
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator
    ): Response {
        $userId = $request->headers->get('userId');
        $errors = $validator->validate(
            compact('userId', 'id'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'id' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $chargingStationManagementManager->getCdrData($userId, $id)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Payment Method')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'paymentMethodUrl', type: 'string')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_BAD_REQUEST,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNAUTHORIZED,
        description: 'Invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message')
                ]),
            ]
        )
    )]
    #[Route('/payment-method-url', name: 'user_payment_method', methods: ['GET'])]
    public function getPaymentMethodUrl(Request $request, ChargingStationManagementManager $chargingStationManagementManager, ValidatorInterface $validator): JsonResponse
    {
        $userId = $request->headers->get('userId', '');

        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }

        $response = $chargingStationManagementManager->getPaymentMethodUrl($userId)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Payment History')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'Gigya UserId',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'string'),
                            new OA\Property(property: 'type', type: 'string', nullable: true),
                            new OA\Property(property: 'transactionTypeId', type: 'integer'),
                            new OA\Property(
                                property: 'transactionType',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'name', type: 'string'),
                                ]
                            ),
                            new OA\Property(property: 'userId', type: 'string'),
                            new OA\Property(property: 'amount', type: 'number'),
                            new OA\Property(property: 'invoiceId', type: 'string', nullable: true),
                            new OA\Property(property: 'walletTransactionId', type: 'string', nullable: true),
                            new OA\Property(property: 'offerTransactionId', type: 'string', nullable: true),
                            new OA\Property(property: 'transactionDate', type: 'string', format: 'date-time'),
                            new OA\Property(property: 'walletTransaction', type: 'string', nullable: true),
                            new OA\Property(property: 'invoice', type: 'string', nullable: true),
                            new OA\Property(property: 'offerTransaction', type: 'string', nullable: true),
                            new OA\Property(
                                property: 'externalTransaction',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'string'),
                                    new OA\Property(property: 'transactionTypeId', type: 'integer'),
                                    new OA\Property(property: 'amount', type: 'number'),
                                    new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                                    new OA\Property(property: 'chargingSessionId', type: 'string'),
                                    new OA\Property(
                                        property: 'chargingSession',
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'id', type: 'string'),
                                            new OA\Property(property: 'locationId', type: 'string'),
                                            new OA\Property(
                                                property: 'location',
                                                type: 'object',
                                                properties: [
                                                    new OA\Property(property: 'id', type: 'string'),
                                                    new OA\Property(property: 'name', type: 'string'),
                                                    new OA\Property(
                                                        property: 'addressDetail',
                                                        type: 'object',
                                                        properties: [
                                                            new OA\Property(property: 'address', type: 'string'),
                                                            new OA\Property(property: 'city', type: 'string'),
                                                            new OA\Property(property: 'country', type: 'string'),
                                                            new OA\Property(property: 'postalCode', type: 'string'),
                                                            new OA\Property(property: 'province', type: 'string', nullable: true),
                                                            new OA\Property(property: 'state', type: 'string'),
                                                        ]
                                                    ),
                                                    new OA\Property(property: 'avgRating', type: 'number'),
                                                    new OA\Property(property: 'cpo', type: 'string'),
                                                    new OA\Property(property: 'cpoId', type: 'string'),
                                                    new OA\Property(property: 'isPaid', type: 'boolean'),
                                                    new OA\Property(property: 'isPromoted', type: 'boolean'),
                                                    new OA\Property(property: 'locationRefId', type: 'string'),
                                                    new OA\Property(property: 'openingHours', type: 'string', nullable: true),
                                                    new OA\Property(property: 'operator', type: 'string'),
                                                    new OA\Property(property: 'power', type: 'number'),
                                                    new OA\Property(property: 'status', type: 'string'),
                                                ]
                                            ),
                                            new OA\Property(property: 'chargingCompletedAt', type: 'string', format: 'date-time'),
                                            new OA\Property(property: 'commissionPercentage', type: 'string'),
                                            new OA\Property(property: 'cost', type: 'string'),
                                            new OA\Property(property: 'cpoId', type: 'string'),
                                            new OA\Property(property: 'energy', type: 'string'),
                                            new OA\Property(property: 'evseId', type: 'string'),
                                            new OA\Property(property: 'fixedCommission', type: 'string'),
                                            new OA\Property(property: 'power', type: 'number'),
                                            new OA\Property(property: 'powerType', type: 'string'),
                                            new OA\Property(property: 'startedAt', type: 'string', format: 'date-time'),
                                            new OA\Property(property: 'status', type: 'string'),
                                            new OA\Property(property: 'vehicleMake', type: 'string'),
                                            new OA\Property(property: 'vehicleModel', type: 'string'),
                                            new OA\Property(property: 'currency', type: 'string'),
                                        ]
                                    )
                                ]
                            )
                        ]
                    )
                )
            ]
        )
    )]

    #[OA\Response(
        response: 400,
        description: 'Operation failed',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[Route('/payment-history', name: 'payment-history', methods: ['GET'])]
    public function getPaymentHistory(Request $request, ChargingStationManagementManager $paymentManager, ValidatorInterface $validator): JsonResponse
    {
        $userId = $request->headers->get('userId', '');

        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }

        $response = $paymentManager->getPaymentHistory($userId)->getArrayFormat();
        $response = $this->removeNullValues($response);

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Vehicle Session')]
    #[Route('/vehicle/active-session', name: 'active_session', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User Id',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'vin',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: Response::HTTP_OK,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'sessionId', type: 'string'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNPROCESSABLE_ENTITY,
        description: 'Validation error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_UNAUTHORIZED,
        description: 'Invalid token',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: Response::HTTP_FORBIDDEN,
        description: 'Access denied due to invalid client credentials',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    public function getActiveSession(
        Request $request,
        ChargingStationManagementManager $chargingStationManagementManager,
        ValidatorInterface $validator,
    ): JsonResponse {
        $userId = $request->headers->get('userId', '');
        $vin = $request->headers->get('vin', '');
        
        $errors = $validator->validate(
            compact('userId', 'vin'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => new Assert\Optional([new Assert\Length(['min' => 17, 'max' => 17])]),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->getArrayFormat();

            return $this->json($response['content'], $response['code']);
        }
        $response = $chargingStationManagementManager->getActiveSession($userId, $vin)->getArrayFormat();
            
        return $this->json($response['content'], $response['code']);
    }

    public function removeNullValues(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            }
            if (is_null($array[$key])) {
                unset($array[$key]);
            }
        }
        return $array;
    }

    function cleanHeaderValue(?string $value): ?string {
        if (!$value) return null;
        $parts = array_unique(array_map('trim', explode(',', $value)));
        return implode(',', $parts);
    }
}
