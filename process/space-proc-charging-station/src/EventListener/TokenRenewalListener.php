<?php

namespace App\EventListener;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\HttpKernel\HttpKernelInterface;

#[AsEventListener(event: ResponseEvent::class, method: 'onKernelResponse')]
class TokenRenewalListener
{
    public const USER_COLLECTION = 'userData';

    public function __construct(
        private HttpKernelInterface $kernel,
        private F2mConnector $connector,
        private MongoAtlasQueryService $mongoService
    ) {
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        $request = $event->getRequest();
        $response = $event->getResponse();
        $statusCode = $response->getStatusCode();
        if ($statusCode !== 401 && $response->getContent() !== '{"error":{"message":"Invalid token."}}') {
            return;
        }

        $userId = $request->headers->get('userId', '');
        $redirectionCount = $request->getSession()->get('redirection_count', 0);
        if ($redirectionCount >= 1) {
            return;
        }
        try {
            if ($userId) {
                $wsResponse = $this->renewAccessToken($userId);
                if ($wsResponse->getCode() == Response::HTTP_OK) {
                    $accessToken = $wsResponse->getData()['success']['data']['accessToken'];
                    $updatedAccessToken = [
                        'f2mc.accessToken' => $accessToken
                    ];

                    $this->mongoService->updateOne(self::USER_COLLECTION, ['userId' => $userId], $updatedAccessToken);

                    // Set the new access token in Authorization header
                    $request->headers->set('Authorization', 'Bearer ' . $accessToken);

                    $request->getSession()->set('redirection_count', $redirectionCount + 1);

                    $this->kernel->handle($request);
                }
            }
        } catch (\Exception $e) {
            throw new UnauthorizedHttpException('Bearer', 'Failed to renew access token: ' . $e->getMessage());
        }
    }

    public function getUserData(string $userId): array
    {
        $userData = $this->mongoService->find(self::USER_COLLECTION, ['userId' => $userId]);
        return json_decode($userData->getData(), true)['documents'][0] ?? [];
    }

    private function getRefreshToken(string $userId)
    {
        $userData = $this->getUserData($userId);
        $refreshToken = $userData['f2mc']['refreshToken'] ?? '';

        return $refreshToken;
    }

    private function renewAccessToken(string $userId)
    {
        $refreshToken = $this->getRefreshToken($userId);
        $headers = [
            'Content-Type' => 'application/json'
        ];

        $payload = [
            'refreshToken' => $refreshToken,
            'role' => 'user',
            'grantType' => 'refresh_token'
        ];

        $response = $this->connector->callF2mc(
            Request::METHOD_POST,
            'v1/user/token',
            [
                'headers' => $headers,
                'json' => $payload,
            ]
        );
        if (Response::HTTP_CREATED == $response->getCode()) {
            $responseData = $response->getData();
            if (isset($responseData['success']['accessToken'])) {
                return new WSResponse($response->getCode(), $responseData['success']);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $response->getData());
        }

        return new WSResponse($response->getCode(), $response->getData());
    }
}
