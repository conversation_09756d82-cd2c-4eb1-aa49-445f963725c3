<?php

namespace App\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Charging Location service.
 */
class ChargingLocationService
{
    public function __construct(
        private F2mConnector $connector,
    ) {
    }

    public function getLocations(array $payload, ?string $sortBy, ?string $orderBy, ?int $offset, ?int $limit, ?string $fromDate, ?string $toDate): WSResponse
    {
        $headers = [
            'Content-Type' => 'application/json',
        ];

        $wsResponse = $this->connector->callF2mc(
            Request::METHOD_POST,
            'v1/charging/locations',
            [
                'headers' => $headers,
                'query' => ['sortBy' => $sortBy, 'orderBy' => $orderBy, 'offset' => $offset, 'limit' => $limit, 'fromDate' => $fromDate, 'toDate' => $toDate],
                'json' => $payload,
            ]
        );

        if (Response::HTTP_OK == $wsResponse->getCode()) {
            $response = $wsResponse->getData();
            if (isset($response['success'])) {
                return new WSResponse($wsResponse->getCode(), $response);
            }

            return new WSResponse(Response::HTTP_NOT_FOUND, $wsResponse->getData());
        }

        return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
    }
}
