<?php

namespace App\Tests\Connector;

use App\Connector\MongoAtlasApiConnector;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class MongoAtlasApiConnectorTest extends TestCase
{
    private HttpClientInterface $client;
    private string $mongoApp;
    private MongoAtlasApiConnector $connector;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->client = $this->createMock(HttpClientInterface::class);
        $this->mongoApp = 'test-app';
        $this->connector = new MongoAtlasApiConnector($this->client, $this->mongoApp);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info');
        $this->logger->expects($this->any())
            ->method('error');
        $this->connector->setLogger($this->logger);
    }

    public function testCallMethodReturnsResponse(): void
    {
        // Mock the HTTP response
        $mockResponse = $this->createMock(ResponseInterface::class);

        $this->client->expects($this->once())
            ->method('request')
            ->with('GET', '/test-url', ['query' => 'param'])
            ->willReturn($mockResponse);

        $response = $this->connector->call('GET', '/test-url', ['query' => 'param']);

        $this->assertSame($mockResponse, $response);
    }

    public function testCallMethodWithDefaultOptions(): void
    {
        // Mock the HTTP response
        $mockResponse = $this->createMock(ResponseInterface::class);

        $this->client->expects($this->once())
            ->method('request')
            ->with('GET', '/test-url', [])
            ->willReturn($mockResponse);

        $response = $this->connector->call('GET', '/test-url');

        $this->assertSame($mockResponse, $response);
    }

    public function testGetEndpointMethodReturnsCorrectEndpoint(): void
    {
        $action = 'find';
        $expectedEndpoint = sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);

        $endpoint = $this->connector->getEndpoint($action);

        $this->assertEquals($expectedEndpoint, $endpoint);
    }

    public function testGetEndpointWithDifferentActions(): void
    {
        $actions = ['find', 'aggregate', 'insertOne', 'updateOne', 'deleteOne'];
        
        foreach ($actions as $action) {
            $expectedEndpoint = sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);
            $endpoint = $this->connector->getEndpoint($action);
            $this->assertEquals($expectedEndpoint, $endpoint);
        }
    }
}