<?php

namespace App\Tests\Connector;

use App\Connector\CustomHttpClient;
use App\Connector\SystemFirebaseConnector;
use App\Helper\WSResponse;
use Exception;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class SystemFirebaseConnectorTest extends TestCase
{
    private CustomHttpClient $client;
    private SystemFirebaseConnector $connector;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        $this->client = $this->createMock(CustomHttpClient::class);
        $this->connector = new SystemFirebaseConnector($this->client, 'https://firebase-api.example.com');
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info');
        $this->logger->expects($this->any())
            ->method('error');
        $this->connector->setLogger($this->logger);
    }

    public function testCallReturnsSuccess(): void
    {
        // Prepare test data
        $method = 'POST';
        $uri = '/v1/push/notification';
        $options = [
            'headers' => [
                'pushToken' => "test-token",
            ],
            'json' => [
                'message' => [
                    'notification' => [
                        'title' => 'Test Title',
                        'body' => 'Test Body'
                    ]
                ]
            ],
        ];
        
        // Mock successful response
        $expectedResponse = new WSResponse(200, ['success' => true]);
        
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, 'https://firebase-api.example.com'.$uri, $options)
            ->willReturn($expectedResponse);

        // Call the method
        $response = $this->connector->call($method, $uri, $options);
        
        // Assert response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
        $this->assertEquals(['success' => true], $response->getData());
    }

    public function testCallWithDefaultOptions(): void
    {
        // Prepare test data
        $method = 'GET';
        $uri = '/v1/status';
        
        // Mock successful response
        $expectedResponse = new WSResponse(200, ['status' => 'ok']);
        
        $this->client->expects($this->once())
            ->method('request')
            ->with($method, 'https://firebase-api.example.com'.$uri, [])
            ->willReturn($expectedResponse);

        // Call the method with default options
        $response = $this->connector->call($method, $uri);
        
        // Assert response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
        $this->assertEquals(['status' => 'ok'], $response->getData());
    }

    public function testCallThrowsException(): void
    {
        // Prepare test data
        $method = 'POST';
        $uri = '/v1/push/notification';
        $options = [
            'headers' => [
                'pushToken' => "invalid-token",
            ],
            'json' => [
                'message' => [
                    'notification' => [
                        'title' => 'Test Title',
                        'body' => 'Test Body'
                    ]
                ]
            ],
        ];
        
        // Mock exception
        $this->client->expects($this->once())
            ->method('request')
            ->willThrowException(new Exception('Invalid token', 401));

        // Call the method
        $response = $this->connector->call($method, $uri, $options);
        
        // Assert response contains exception details
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(401, $response->getCode());
        $this->assertEquals('Invalid token', $response->getData());
    }
}