<?php

namespace App\Tests\Controller;

use App\Controller\ChargingStationManagementController;
use App\Helper\ErrorResponse;
use App\Manager\ChargingStationManagementManager;
use App\Manager\UserManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingStationManagementControllerTest extends WebTestCase
{
    private $chargingStationManagementController;
    private $chargingStationManagementManager;
    private $userManager;
    private $validator;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->chargingStationManagementManager = $this->createMock(ChargingStationManagementManager::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->chargingStationManagementController = new ChargingStationManagementController();
        $this->chargingStationManagementController->setContainer(static::getContainer());
    }

    private function jsonResponse($response): \Symfony\Component\HttpFoundation\JsonResponse
    {
        // Helper method to mimic the controller's jsonResponse method
        $arrayFormat = $response->getArrayFormat();
        return new \Symfony\Component\HttpFoundation\JsonResponse($arrayFormat['content'], $arrayFormat['code']);
    }

    /**
     * Helper method to create a mock IResponseArrayFormat
     */
    private function createMockResponse(array $data, int $code = Response::HTTP_OK): \App\Helper\IResponseArrayFormat
    {
        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => $code,
                'content' => ['success' => $data]
            ]);
        $mockResponse->method('getJsonFormat')
            ->willReturn(new JsonResponse(['success' => $data], $code));

        return $mockResponse;
    }

    /**
     * Helper method to create a mock error response
     */
    private function createMockErrorResponse(string $message, int $code = Response::HTTP_BAD_REQUEST): \App\Helper\IResponseArrayFormat
    {
        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => $code,
                'content' => ['error' => ['message' => $message]]
            ]);
        $mockResponse->method('getJsonFormat')
            ->willReturn(new JsonResponse(['error' => ['message' => $message]], $code));

        return $mockResponse;
    }

    public function testHistoryReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123']; // Changed to HTTP_userId
        $query = [
            'cpo' => 'test-cpo',
            'from' => '2023-01-01',
            'to' => '2023-01-31',
            'status' => 'COMPLETED'
        ];

        $request = Request::create('/v1/charging-station/history', Request::METHOD_GET, $query, [], [], $headers);

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $expectedResponse = [
            'sessions' => [
                [
                    'id' => 'session123',
                    'startDate' => '2023-01-15T10:00:00Z',
                    'endDate' => '2023-01-15T11:30:00Z',
                    'status' => 'COMPLETED'
                ]
            ]
        ];

        // Create a mock SuccessResponse that will return the expected array format
        $mockSuccessResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockSuccessResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $expectedResponse]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getHistory')
            ->with(
                [
                    'cpo' => 'test-cpo',
                    'from' => '2023-01-01',
                    'to' => '2023-01-31',
                    'status' => 'COMPLETED'
                ],
                'user123'
            )
            ->willReturn($mockSuccessResponse);

        $response = $this->chargingStationManagementController->history(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'sessions' => [
                [
                    'id' => 'session123',
                    'startDate' => '2023-01-15T10:00:00Z',
                    'endDate' => '2023-01-15T11:30:00Z',
                    'status' => 'COMPLETED'
                ]
            ]
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testHistoryReturnsValidationError(): void
    {
        $headers = []; // Missing userId
        $query = [
            'cpo' => 'test-cpo',
            'from' => '2023-01-01',
            'to' => '2023-01-31',
            'status' => 'COMPLETED'
        ];

        $request = Request::create('/v1/charging-station/history', Request::METHOD_GET, $query, [], [], $headers);

        // Create a constraint violation
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            'userId',
            'userId',
            null
        );

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingStationManagementController->history(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testIshowroomEligibilityReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123']; // Changed to HTTP_userId
        $content = ['email' => '<EMAIL>'];

        $request = Request::create(
            '/v1/charging-station/ishowroom/eligibility',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $expectedResponse = [
            'eligibility' => [
                'isEligible' => true,
                'credits' => 100
            ]
        ];

        // Create a mock SuccessResponse that will return the expected array format
        $mockSuccessResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockSuccessResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $expectedResponse]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getIshowroomEligibility')
            ->with('user123', '<EMAIL>')
            ->willReturn($mockSuccessResponse);

        $response = $this->chargingStationManagementController->ishowroomEligibility(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'eligibility' => [
                'isEligible' => true,
                'credits' => 100
            ]
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testIshowroomEligibilityReturnsValidationError(): void
    {
        $headers = ['HTTP_userId' => 'user123']; // Changed to HTTP_userId
        $content = ['email' => 'invalid-email']; // Invalid email

        $request = Request::create(
            '/v1/charging-station/ishowroom/eligibility',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Create a constraint violation
        $constraint = new ConstraintViolation(
            'This value is not a valid email address.',
            null,
            [],
            'email',
            'email',
            'invalid-email'
        );

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingStationManagementController->ishowroomEligibility(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('email', $response->getContent());
    }

    public function testRegisterReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123']; // Changed to HTTP_userId
        $content = ['vin' => 'VF7KFNFVBHS123456']; // Valid 17-char VIN

        $request = Request::create(
            '/v1/charging-station/register',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $expectedResponse = [
            'registered' => true,
            'message' => 'Successfully registered'
        ];

        // Create a mock SuccessResponse that will return the expected array format
        $mockSuccessResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockSuccessResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $expectedResponse]
            ]);
        $mockSuccessResponse->method('getJsonFormat')
            ->willReturn(new JsonResponse(['success' => $expectedResponse], Response::HTTP_OK));

        $this->userManager->expects($this->once())
            ->method('register')
            ->with($content, 'user123')
            ->willReturn($mockSuccessResponse);

        $response = $this->chargingStationManagementController->register(
            $request,
            $this->userManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'registered' => true,
            'message' => 'Successfully registered'
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testRegisterReturnsValidationError(): void
    {
        $headers = ['HTTP_userId' => 'user123']; // Changed to HTTP_userId
        $content = ['vin' => '12345']; // Invalid VIN (too short)

        $request = Request::create(
            '/v1/charging-station/register',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Create a constraint violation
        $constraint = new ConstraintViolation(
            'This value should have exactly 17 characters.',
            null,
            [],
            'vin',
            'vin',
            '12345'
        );

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingStationManagementController->register(
            $request,
            $this->userManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('vin', $response->getContent());
    }

    public function testGetWalletDetailReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123'];
        $request = Request::create('/v1/charging-station/wallet', Request::METHOD_GET, [], [], [], $headers);

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $walletData = [
            'balance' => 100.50,
            'currency' => 'USD',
            'lastUpdated' => '2023-05-14T10:00:00Z'
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $walletData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getWalletDetail')
            ->with('user123')
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getWalletDetail(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"balance":100.5,"currency":"USD","lastUpdated":"2023-05-14T10:00:00Z"}}', $response->getContent());
    }

    public function testGetWalletDetailReturnsValidationError(): void
    {
        $headers = []; // Missing userId
        $request = Request::create('/v1/charging-station/wallet', Request::METHOD_GET, [], [], [], $headers);

        // Create a constraint violation
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            'userId',
            'userId',
            null
        );

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingStationManagementController->getWalletDetail(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testSessionStopReturnsSuccessfulResponse(): void
    {
        $sessionId = 'session123';
        $headers = ['HTTP_userId' => 'user123'];
        $request = Request::create(
            '/v1/charging-station/session/' . $sessionId . '/stop',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = ['message' => 'Session stopped successfully'];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('stopSession')
            ->with('user123', $sessionId)
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->sessionStop(
            $sessionId,
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'message' => 'Session stopped successfully'
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testSessionStopReturnsValidationError(): void
    {
        $sessionId = 'session123';
        $headers = []; // Missing userId
        $request = Request::create(
            '/v1/charging-station/session/' . $sessionId . '/stop',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers
        );

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'userId',
                'userId',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->sessionStop(
            $sessionId,
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testSessionStartReturnsSuccessfulResponse(): void
    {
        $headers = [
            'HTTP_userId' => 'user123',
            'HTTP_vin' => 'VF7KFNFVBHS123456',
            'HTTP_locationId' => 'location123',
            'HTTP_evseId' => 'evse123',
            'HTTP_lat' => '37.7749',
            'HTTP_long' => '-122.4194',
            'HTTP_connectorId' => 'connector123'
        ];

        $request = Request::create(
            '/v1/charging-station/session/start',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers
        );

        // Create a mock logger
        $logger = $this->createMock(\Psr\Log\LoggerInterface::class);

        // Set the logger in the controller using reflection
        $reflection = new \ReflectionClass($this->chargingStationManagementController);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->chargingStationManagementController, $logger);

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = ['message' => 'Session started successfully'];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('startSession')
            ->with(
                'user123',
                'VF7KFNFVBHS123456',
                [
                    'locationId' => 'location123',
                    'evseId' => 'evse123',
                    'connectorId' => 'connector123',
                    'long' => '-122.4194',
                    'lat' => '37.7749'
                ]
            )
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->sessionStart(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => ['message' => 'Session started successfully']];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testSessionStartReturnsValidationError(): void
    {
        $headers = [
            'HTTP_userId' => 'user123',
            // Missing required headers
        ];

        $request = Request::create(
            '/v1/charging-station/session/start',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers
        );

        // Create a mock logger
        $logger = $this->createMock(\Psr\Log\LoggerInterface::class);

        // Set the logger in the controller using reflection
        $reflection = new \ReflectionClass($this->chargingStationManagementController);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->chargingStationManagementController, $logger);

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'vin',
                'vin',
                null
            ),
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'locationId',
                'locationId',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->sessionStart(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('vin', $response->getContent());
        $this->assertStringContainsString('locationId', $response->getContent());
    }

    public function testApplyIshowroomCreditReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123'];
        $content = [
            'id' => 'credit123'
        ];

        $request = Request::create(
            '/v1/charging-station/ishowroom/credit',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            'message' => 'The credit applied successfully.',
            'name' => 'APPLY_CREDIT_SUCCESS'
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with('user123', 'credit123')
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->applyIshowroomCredit(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'message' => 'The credit applied successfully.',
            'name' => 'APPLY_CREDIT_SUCCESS'
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testApplyIshowroomCreditReturnsValidationError(): void
    {
        $headers = ['HTTP_userId' => 'user123'];
        $content = []; // Missing id

        $request = Request::create(
            '/v1/charging-station/ishowroom/credit',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'id',
                'id',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->applyIshowroomCredit(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('id', $response->getContent());
    }

    public function testApplyIshowroomCreditHandlesException(): void
    {
        $headers = ['HTTP_userId' => 'user123'];
        $content = [
            'id' => 'credit123'
        ];

        $request = Request::create(
            '/v1/charging-station/ishowroom/credit',
            Request::METHOD_POST,
            [],
            [],
            [],
            $headers,
            json_encode($content)
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager to throw an exception
        $this->chargingStationManagementManager->expects($this->once())
            ->method('applyIshowroomCredit')
            ->with('user123', 'credit123')
            ->willThrowException(new \Exception('Test exception'));

        $response = $this->chargingStationManagementController->applyIshowroomCredit(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertStringContainsString('error', $response->getContent());
        $this->assertStringContainsString('Test exception', $response->getContent());
    }

    public function testGetSessionReturnsSuccessfulResponse(): void
    {
        $sessionId = 'session123';
        $headers = ['HTTP_userId' => 'user123'];

        $request = Request::create(
            '/v1/charging-station/session/' . $sessionId,
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            'id' => 'session123',
            'status' => 'ACTIVE',
            'startTime' => '2023-05-14T10:00:00Z',
            'duration' => 3600,
            'energy' => 15.5,
            'cost' => 12.75
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getSession')
            ->with('user123', $sessionId)
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getSession(
            $sessionId,
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'id' => 'session123',
            'status' => 'ACTIVE',
            'startTime' => '2023-05-14T10:00:00Z',
            'duration' => 3600,
            'energy' => 15.5,
            'cost' => 12.75
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testGetCdrDataReturnsSuccessfulResponse(): void
    {
        $sessionId = 'session123';
        $headers = ['HTTP_userId' => 'user123'];

        $request = Request::create(
            '/v1/charging-station/session/' . $sessionId . '/cdr',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            'sessionId' => 'session123',
            'startTime' => '2023-05-14T10:00:00Z',
            'endTime' => '2023-05-14T11:00:00Z',
            'energy' => 15.5,
            'cost' => 12.75,
            'currency' => 'USD'
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getCdrData')
            ->with('user123', $sessionId)
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getCdrData(
            $sessionId,
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'sessionId' => 'session123',
            'startTime' => '2023-05-14T10:00:00Z',
            'endTime' => '2023-05-14T11:00:00Z',
            'energy' => 15.5,
            'cost' => 12.75,
            'currency' => 'USD'
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testGetCdrDataReturnsValidationError(): void
    {
        $sessionId = 'session123';
        $headers = []; // Missing userId

        $request = Request::create(
            '/v1/charging-station/session/' . $sessionId . '/cdr',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'userId',
                'userId',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->getCdrData(
            $sessionId,
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testGetPaymentMethodUrlReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123'];

        $request = Request::create(
            '/v1/charging-station/payment/method-url',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            'paymentMethodUrl' => 'https://payment-provider.com/setup?token=abc123'
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getPaymentMethodUrl')
            ->with('user123')
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getPaymentMethodUrl(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => ['paymentMethodUrl' => 'https://payment-provider.com/setup?token=abc123']];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testGetPaymentMethodUrlReturnsValidationError(): void
    {
        $headers = []; // Missing userId

        $request = Request::create(
            '/v1/charging-station/payment/method-url',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'userId',
                'userId',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->getPaymentMethodUrl(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testGetPaymentHistoryReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123'];

        $request = Request::create(
            '/v1/charging-station/payment-history',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            [
                'id' => 'tx123',
                'type' => 'CHARGE',
                'transactionTypeId' => 1,
                'transactionType' => ['name' => 'Charge'],
                'userId' => 'user123',
                'amount' => 12.75,
                'transactionDate' => '2023-01-15T10:00:00Z'
            ]
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getPaymentHistory')
            ->with('user123')
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getPaymentHistory(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            [
                'id' => 'tx123',
                'type' => 'CHARGE',
                'transactionTypeId' => 1,
                'transactionType' => ['name' => 'Charge'],
                'userId' => 'user123',
                'amount' => 12.75,
                'transactionDate' => '2023-01-15T10:00:00Z'
            ]
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testGetPaymentHistoryReturnsValidationError(): void
    {
        $headers = []; // Missing userId

        $request = Request::create(
            '/v1/charging-station/payment-history',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'userId',
                'userId',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->getPaymentHistory(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testGetActiveSessionReturnsSuccessfulResponse(): void
    {
        $headers = ['HTTP_userId' => 'user123'];

        $request = Request::create(
            '/v1/charging-station/session/active',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            'session' => [
                'id' => 'session123',
                'status' => 'ACTIVE',
                'startTime' => '2023-05-14T10:00:00Z',
                'duration' => 3600,
                'energy' => 15.5,
                'cost' => 12.75
            ]
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getActiveSession')
            ->with('user123', '')
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getActiveSession(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'session' => [
                'id' => 'session123',
                'status' => 'ACTIVE',
                'startTime' => '2023-05-14T10:00:00Z',
                'duration' => 3600,
                'energy' => 15.5,
                'cost' => 12.75
            ]
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testGetActiveSessionWithVinReturnsSuccessfulResponse(): void
    {
        $headers = [
            'HTTP_userId' => 'user123',
            'HTTP_vin' => 'VF7KFNFVBHS123456'
        ];

        $request = Request::create(
            '/v1/charging-station/session/active',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Mock manager response
        $responseData = [
            'session' => [
                'id' => 'session123',
                'status' => 'ACTIVE',
                'startTime' => '2023-05-14T10:00:00Z',
                'duration' => 3600,
                'energy' => 15.5,
                'cost' => 12.75,
                'vin' => 'VF7KFNFVBHS123456'
            ]
        ];

        $mockResponse = $this->createMock(\App\Helper\IResponseArrayFormat::class);
        $mockResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => ['success' => $responseData]
            ]);

        $this->chargingStationManagementManager->expects($this->once())
            ->method('getActiveSession')
            ->with('user123', 'VF7KFNFVBHS123456')
            ->willReturn($mockResponse);

        $response = $this->chargingStationManagementController->getActiveSession(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        // Use json_decode to compare the content instead of string comparison to avoid issues with escaping
        $expectedContent = ['success' => [
            'session' => [
                'id' => 'session123',
                'status' => 'ACTIVE',
                'startTime' => '2023-05-14T10:00:00Z',
                'duration' => 3600,
                'energy' => 15.5,
                'cost' => 12.75,
                'vin' => 'VF7KFNFVBHS123456'
            ]
        ]];
        $actualContent = json_decode($response->getContent(), true);
        $this->assertEquals($expectedContent, $actualContent);
    }

    public function testGetActiveSessionReturnsValidationError(): void
    {
        $headers = []; // Missing userId

        $request = Request::create(
            '/v1/charging-station/session/active',
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Create constraint violations
        $violations = new ConstraintViolationList([
            new ConstraintViolation(
                'This value should not be blank.',
                null,
                [],
                'userId',
                'userId',
                null
            )
        ]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn($violations);

        $response = $this->chargingStationManagementController->getActiveSession(
            $request,
            $this->chargingStationManagementManager,
            $this->validator
        );

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
        $this->assertStringContainsString('userId', $response->getContent());
    }

    public function testRemoveNullValues(): void
    {
        $input = [
            'key1' => 'value1',
            'key2' => null,
            'key3' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey2' => null
            ],
            'key4' => [
                'deeplyNested' => [
                    'deepKey1' => 'deepValue1',
                    'deepKey2' => null
                ]
            ]
        ];

        $expected = [
            'key1' => 'value1',
            'key3' => [
                'nestedKey1' => 'nestedValue1'
            ],
            'key4' => [
                'deeplyNested' => [
                    'deepKey1' => 'deepValue1'
                ]
            ]
        ];

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($this->chargingStationManagementController);
        $method = $reflection->getMethod('removeNullValues');
        $method->setAccessible(true);

        $result = $method->invoke($this->chargingStationManagementController, $input);

        $this->assertEquals($expected, $result);
    }

    public function testCleanHeaderValue(): void
    {
        // Use reflection to access the private method
        $reflection = new \ReflectionClass($this->chargingStationManagementController);
        $method = $reflection->getMethod('cleanHeaderValue');
        $method->setAccessible(true);

        // Test with null value
        $this->assertNull($method->invoke($this->chargingStationManagementController, null));

        // Test with empty string
        $this->assertNull($method->invoke($this->chargingStationManagementController, ''));

        // Test with single value
        $this->assertEquals('value', $method->invoke($this->chargingStationManagementController, 'value'));

        // Test with multiple values and spaces
        $this->assertEquals('value1,value2', $method->invoke($this->chargingStationManagementController, ' value1 , value2 '));

        // Test with duplicate values
        $this->assertEquals('value', $method->invoke($this->chargingStationManagementController, 'value,value'));
    }
}
