<?php

namespace App\Tests\Controller;

use App\Controller\UserController;
use App\Manager\UserManager;
use App\Helper\IResponseArrayFormat;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserControllerTest extends WebTestCase
{
    private $userController;
    private $userManager;
    private $validator;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->userController = new UserController();
        $this->userController->setContainer(static::getContainer());
    }

    public function testGetAccountLinkUrlReturnsSuccessfulResponse(): void
    {
        // Test data
        $id = 'CPT'; // Provider short code
        $userId = 'user123';
        $headers = ['HTTP_userId' => $userId];

        // Create request
        $request = Request::create(
            '/v1/user/account-link-url/' . $id,
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Expected response data
        $responseData = [
            'accountLinkingUrl' => 'https://example.com/link',
            'accountLinkingRedirectUrl' => 'https://example.com/redirect'
        ];

        // Create a mock SuccessResponse
        $mockSuccessResponse = $this->createMock(IResponseArrayFormat::class);
        $mockSuccessResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_OK,
                'content' => [
                    'success' => $responseData
                ]
            ]);

        // Mock UserManager to return the success response
        $this->userManager->expects($this->once())
            ->method('getAccountLinkUrl')
            ->with($userId, $id)
            ->willReturn($mockSuccessResponse);

        // Call the controller method
        $response = $this->userController->getAccountLinkUrl(
            $id,
            $request,
            $this->userManager,
            $this->validator
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseContent);
        $this->assertEquals($responseData, $responseContent['success']);
    }

    public function testGetAccountLinkUrlReturnsValidationError(): void
    {
        // Test data
        $id = 'CPT'; // Provider short code
        $headers = []; // Missing userId header

        // Create request
        $request = Request::create(
            '/v1/user/account-link-url/' . $id,
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Create a constraint violation for missing userId
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            'userId',
            'userId',
            null
        );

        // Mock validator to return violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList([$constraint]));

        // Call the controller method
        $response = $this->userController->getAccountLinkUrl(
            $id,
            $request,
            $this->userManager,
            $this->validator
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals('validation_failed', $responseContent['error']['message']);
        $this->assertArrayHasKey('userId', $responseContent['error']['errors']);
    }

    public function testGetAccountLinkUrlReturnsErrorResponse(): void
    {
        // Test data
        $id = 'CPT'; // Provider short code
        $userId = 'user123';
        $headers = ['HTTP_userId' => $userId];

        // Create request
        $request = Request::create(
            '/v1/user/account-link-url/' . $id,
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers
        );

        // Mock validator to return no violations
        $this->validator->expects($this->once())
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        // Create a mock ErrorResponse
        $errorMessage = 'Token not found';
        $mockErrorResponse = $this->createMock(IResponseArrayFormat::class);
        $mockErrorResponse->method('getArrayFormat')
            ->willReturn([
                'code' => Response::HTTP_BAD_REQUEST,
                'content' => [
                    'error' => [
                        'message' => $errorMessage
                    ]
                ]
            ]);

        // Mock UserManager to return the error response
        $this->userManager->expects($this->once())
            ->method('getAccountLinkUrl')
            ->with($userId, $id)
            ->willReturn($mockErrorResponse);

        // Call the controller method
        $response = $this->userController->getAccountLinkUrl(
            $id,
            $request,
            $this->userManager,
            $this->validator
        );

        // Assert response
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseContent);
        $this->assertEquals($errorMessage, $responseContent['error']['message']);
    }

    public function testRemoveNullValues(): void
    {
        // Test with a simple array containing null values
        $input = [
            'key1' => 'value1',
            'key2' => null,
            'key3' => 'value3'
        ];

        $expected = [
            'key1' => 'value1',
            'key3' => 'value3'
        ];

        $result = $this->userController->removeNullValues($input);
        $this->assertEquals($expected, $result);

        // Test with a nested array containing null values
        $nestedInput = [
            'key1' => 'value1',
            'key2' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey2' => null,
                'nestedKey3' => [
                    'deepKey1' => 'deepValue1',
                    'deepKey2' => null
                ]
            ],
            'key3' => null
        ];

        $nestedExpected = [
            'key1' => 'value1',
            'key2' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey3' => [
                    'deepKey1' => 'deepValue1'
                ]
            ]
        ];

        $nestedResult = $this->userController->removeNullValues($nestedInput);
        $this->assertEquals($nestedExpected, $nestedResult);

        // Test with an empty array
        $emptyInput = [];
        $emptyResult = $this->userController->removeNullValues($emptyInput);
        $this->assertEquals($emptyInput, $emptyResult);
    }
}
