<?php

namespace App\Tests\EventListener;

use App\EventListener\ExceptionListener;
use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\HttpFoundation\Request;

class ExceptionListenerTest extends TestCase
{
    private $exceptionListener;
    private $exceptionEvent;
    private $kernel;

    protected function setUp(): void
    {
        $this->exceptionListener = new ExceptionListener();
        $this->kernel = $this->createMock(HttpKernelInterface::class);
    }

    public function testOnKernelExceptionWithDefaultCode(): void
    {
        // Create a mock exception with no specific code
        $exception = new \Exception('Test exception message');

        // Create the event
        $request = new Request();
        $this->exceptionEvent = new ExceptionEvent(
            $this->kernel,
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        // Call the listener
        $this->exceptionListener->onKernelException($this->exceptionEvent);

        // Get the response
        $response = $this->exceptionEvent->getResponse();

        // Assert response is a JsonResponse
        $this->assertInstanceOf(JsonResponse::class, $response);

        // Assert response code is HTTP_INTERNAL_SERVER_ERROR (500)
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());

        // Assert response content contains the exception message
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('content', $responseContent);
        $this->assertArrayHasKey('error', $responseContent['content']);
        $this->assertArrayHasKey('message', $responseContent['content']['error']);
        $this->assertEquals('Test exception message', $responseContent['content']['error']['message']);
    }

    public function testOnKernelExceptionWithCustomCode(): void
    {
        // Create a mock exception with a specific code
        $exception = new \Exception('Custom code exception', Response::HTTP_BAD_REQUEST);

        // Create the event
        $request = new Request();
        $this->exceptionEvent = new ExceptionEvent(
            $this->kernel,
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        // Call the listener
        $this->exceptionListener->onKernelException($this->exceptionEvent);

        // Get the response
        $response = $this->exceptionEvent->getResponse();

        // Assert response is a JsonResponse
        $this->assertInstanceOf(JsonResponse::class, $response);

        // Assert response code is HTTP_BAD_REQUEST (400)
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        // Assert response content contains the exception message
        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('content', $responseContent);
        $this->assertArrayHasKey('error', $responseContent['content']);
        $this->assertArrayHasKey('message', $responseContent['content']['error']);
        $this->assertEquals('Custom code exception', $responseContent['content']['error']['message']);
    }
}
