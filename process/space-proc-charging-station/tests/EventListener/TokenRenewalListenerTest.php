<?php

namespace App\Tests\EventListener;

use App\Connector\F2mConnector;
use App\EventListener\TokenRenewalListener;
use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;

class TokenRenewalListenerTest extends TestCase
{
    private $tokenRenewalListener;
    private $kernel;
    private $connector;
    private $mongoService;
    private $responseEvent;
    private $request;
    private $response;
    private $session;

    protected function setUp(): void
    {
        $this->kernel = $this->createMock(HttpKernelInterface::class);
        $this->connector = $this->createMock(F2mConnector::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);

        $this->tokenRenewalListener = new TokenRenewalListener(
            $this->kernel,
            $this->connector,
            $this->mongoService
        );

        $this->request = $this->createMock(Request::class);
        $this->response = $this->createMock(Response::class);
        $this->session = $this->createMock(SessionInterface::class);

        // Setup request headers
        $headerBag = $this->createMock(HeaderBag::class);
        $this->request->headers = $headerBag;

        // Setup request session
        $this->request->method('getSession')->willReturn($this->session);
    }

    public function testOnKernelResponseIgnoresNon401Responses(): void
    {
        // Setup response with non-401 status code
        $this->response->method('getStatusCode')->willReturn(200);
        $this->response->method('getContent')->willReturn('{"success": true}');

        // Create response event
        $this->responseEvent = new ResponseEvent(
            $this->kernel,
            $this->request,
            HttpKernelInterface::MAIN_REQUEST,
            $this->response
        );

        // The kernel should not be called for non-401 responses
        $this->kernel->expects($this->never())->method('handle');

        // Call the listener
        $this->tokenRenewalListener->onKernelResponse($this->responseEvent);
    }

    public function testOnKernelResponseIgnoresWhenRedirectionCountExceeded(): void
    {
        // Setup response with 401 status code
        $this->response->method('getStatusCode')->willReturn(401);
        $this->response->method('getContent')->willReturn('{"error":{"message":"Invalid token."}}');

        // Setup request with userId
        $this->request->headers->method('get')->with('userId', '')->willReturn('user123');

        // Setup session with redirection count >= 1
        $this->session->method('get')->with('redirection_count', 0)->willReturn(1);

        // Create response event
        $this->responseEvent = new ResponseEvent(
            $this->kernel,
            $this->request,
            HttpKernelInterface::MAIN_REQUEST,
            $this->response
        );

        // The kernel should not be called when redirection count is exceeded
        $this->kernel->expects($this->never())->method('handle');

        // Call the listener
        $this->tokenRenewalListener->onKernelResponse($this->responseEvent);
    }

    public function testOnKernelResponseRenewsTokenSuccessfully(): void
    {
        // Setup response with 401 status code
        $this->response->method('getStatusCode')->willReturn(401);
        $this->response->method('getContent')->willReturn('{"error":{"message":"Invalid token."}}');

        // Setup request with userId
        $this->request->headers->method('get')->with('userId', '')->willReturn('user123');

        // Setup session with redirection count = 0
        $this->session->method('get')->with('redirection_count', 0)->willReturn(0);

        // Mock getUserData to return user data with refresh token
        $userData = [
            'documents' => [
                [
                    'f2mc' => [
                        'refreshToken' => 'refresh-token-123'
                    ]
                ]
            ]
        ];

        $userDataResponse = $this->createMock(WSResponse::class);
        $userDataResponse->method('getData')->willReturn(json_encode($userData));

        $this->mongoService->method('find')
            ->with(TokenRenewalListener::USER_COLLECTION, ['userId' => 'user123'])
            ->willReturn($userDataResponse);

        // Mock renewAccessToken to return a successful response
        $tokenResponse = [
            'success' => [
                'data' => [
                    'accessToken' => 'new-access-token-123'
                ]
            ]
        ];

        $wsResponse = $this->createMock(WSResponse::class);
        $wsResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $wsResponse->method('getData')->willReturn($tokenResponse);

        $this->connector->method('callF2mc')
            ->willReturn($wsResponse);

        // Expect updateOne to be called with the new access token
        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(
                TokenRenewalListener::USER_COLLECTION,
                ['userId' => 'user123'],
                ['f2mc.accessToken' => 'new-access-token-123']
            );

        // Expect headers to be updated with the new access token
        $this->request->headers->expects($this->once())
            ->method('set')
            ->with('Authorization', 'Bearer new-access-token-123');

        // Expect session to be updated with incremented redirection count
        $this->session->expects($this->once())
            ->method('set')
            ->with('redirection_count', 1);

        // Expect kernel to handle the request with the new token
        $this->kernel->expects($this->once())
            ->method('handle')
            ->with($this->request);

        // Create response event
        $this->responseEvent = new ResponseEvent(
            $this->kernel,
            $this->request,
            HttpKernelInterface::MAIN_REQUEST,
            $this->response
        );

        // Call the listener
        $this->tokenRenewalListener->onKernelResponse($this->responseEvent);
    }

    public function testGetUserData(): void
    {
        $userData = [
            'documents' => [
                [
                    'userId' => 'user123',
                    'f2mc' => [
                        'refreshToken' => 'refresh-token-123'
                    ]
                ]
            ]
        ];

        $userDataResponse = $this->createMock(WSResponse::class);
        $userDataResponse->method('getData')->willReturn(json_encode($userData));

        $this->mongoService->method('find')
            ->with(TokenRenewalListener::USER_COLLECTION, ['userId' => 'user123'])
            ->willReturn($userDataResponse);

        $result = $this->tokenRenewalListener->getUserData('user123');

        $this->assertEquals($userData['documents'][0], $result);
    }

    public function testGetUserDataWithEmptyResponse(): void
    {
        $userDataResponse = $this->createMock(WSResponse::class);
        $userDataResponse->method('getData')->willReturn('{}');

        $this->mongoService->method('find')
            ->with(TokenRenewalListener::USER_COLLECTION, ['userId' => 'user123'])
            ->willReturn($userDataResponse);

        $result = $this->tokenRenewalListener->getUserData('user123');

        $this->assertEquals([], $result);
    }

    public function testOnKernelResponseWithExceptionThrown(): void
    {
        // Setup response with 401 status code
        $this->response->method('getStatusCode')->willReturn(401);
        $this->response->method('getContent')->willReturn('{"error":{"message":"Invalid token."}}');

        // Setup request with userId
        $this->request->headers->method('get')->with('userId', '')->willReturn('user123');

        // Setup session with redirection count = 0
        $this->session->method('get')->with('redirection_count', 0)->willReturn(0);

        // Mock getUserData to throw an exception
        $this->mongoService->method('find')
            ->willThrowException(new \Exception('Database connection error'));

        // Create response event
        $this->responseEvent = new ResponseEvent(
            $this->kernel,
            $this->request,
            HttpKernelInterface::MAIN_REQUEST,
            $this->response
        );

        // Expect exception to be thrown
        $this->expectException(\Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException::class);
        $this->expectExceptionMessage('Failed to renew access token: Database connection error');

        // Call the listener
        $this->tokenRenewalListener->onKernelResponse($this->responseEvent);
    }
}
