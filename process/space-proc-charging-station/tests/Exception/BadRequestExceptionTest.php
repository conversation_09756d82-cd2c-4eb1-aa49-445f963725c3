<?php

namespace App\Tests\Exception;

use App\Exception\BadRequestException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class BadRequestExceptionTest extends TestCase
{
    public function testMake(): void
    {
        $exception = BadRequestException::make();
        
        $this->assertInstanceOf(BadRequestException::class, $exception);
        $this->assertEquals('Bad Request !', $exception->getMessage());
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $exception->getCode());
    }
}
