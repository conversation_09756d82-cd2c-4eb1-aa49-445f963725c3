<?php

namespace App\Tests\Exception;

use App\Exception\MongoAtlasUrlNotProvidedException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class MongoAtlasUrlNotProvidedExceptionTest extends TestCase
{
    public function testMake(): void
    {
        $exception = MongoAtlasUrlNotProvidedException::make();
        
        $this->assertInstanceOf(MongoAtlasUrlNotProvidedException::class, $exception);
        $this->assertEquals('MongoAtlas error!', $exception->getMessage());
        $this->assertEquals(Response::HTTP_NOT_FOUND, $exception->getCode());
    }
}
