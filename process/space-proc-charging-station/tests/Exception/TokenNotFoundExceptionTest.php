<?php

namespace App\Tests\Exception;

use App\Exception\TokenNotFoundException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class TokenNotFoundExceptionTest extends TestCase
{
    public function testMake(): void
    {
        $exception = TokenNotFoundException::make();
        
        $this->assertInstanceOf(TokenNotFoundException::class, $exception);
        $this->assertEquals('Bad Request !', $exception->getMessage());
        $this->assertEquals(Response::HTTP_NOT_FOUND, $exception->getCode());
    }
}
