<?php

namespace App\Tests\Exception;

use App\Exception\UrlNotFoundException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class UrlNotFoundExceptionTest extends TestCase
{
    public function testMake(): void
    {
        $exception = UrlNotFoundException::make();
        
        $this->assertInstanceOf(UrlNotFoundException::class, $exception);
        $this->assertEquals('Url not found !', $exception->getMessage());
        $this->assertEquals(Response::HTTP_NOT_FOUND, $exception->getCode());
    }
}
