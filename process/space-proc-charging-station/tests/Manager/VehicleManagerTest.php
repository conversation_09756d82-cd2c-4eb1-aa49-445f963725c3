<?php

namespace App\Tests\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\VehicleManager;
use App\Service\VehicleService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleManagerTest extends TestCase
{
    private VehicleManager $manager;
    private VehicleService $service;
    private ValidatorInterface $validator;
    private LoggerInterface $logger;

    public function setUp(): void
    {
        parent::setUp();

        $this->service = $this->createMock(VehicleService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->manager = new VehicleManager(
            $this->service,
            $this->validator
        );

        $this->manager->setLogger($this->logger);
    }

    public function testValidateVehicleDataReturnsViolationList(): void
    {
        $data = [
            'vin' => 'VIN123456789012345',
            'make' => 'Test Make',
            'model' => 'Test Model',
            'vehicleId' => '12345'
        ];
        
        $violations = $this->createMock(ConstraintViolationListInterface::class);
        
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, $this->anything())
            ->willReturn($violations);
        
        $result = $this->manager->validateVehicleData($data);
        
        $this->assertSame($violations, $result);
    }

    public function testAddVehicleReturnsSuccessfulResponse(): void
    {
        $data = [
            'params' => [
                'vin' => 'VIN123456789012345'
            ],
            'accessToken' => 'test-token'
        ];
        
        $serviceResponse = new WSResponse(Response::HTTP_CREATED, [
            'success' => [
                'id' => '12345',
                'vin' => 'VIN123456789012345',
                'make' => 'Test Make',
                'model' => 'Test Model'
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->add($data);
        
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([
            'id' => '12345',
            'vin' => 'VIN123456789012345',
            'make' => 'Test Make',
            'model' => 'Test Model'
        ], $result->getData());
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
    }

    public function testAddVehicleReturnsErrorResponseOnFailure(): void
    {
        $data = [
            'params' => [
                'vin' => 'INVALID_VIN'
            ],
            'accessToken' => 'test-token'
        ];
        
        $serviceResponse = new WSResponse(Response::HTTP_BAD_REQUEST, [
            'error' => [
                'message' => 'Invalid VIN format'
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->add($data);
        
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid VIN format', $result->getMessage());
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
    }

    public function testGetListReturnsSuccessfulResponse(): void
    {
        $accessToken = 'test-token';
        
        $serviceResponse = new WSResponse(Response::HTTP_OK, [
            'success' => [
                [
                    'id' => '12345',
                    'vin' => 'VIN123456789012345',
                    'make' => 'Test Make',
                    'model' => 'Test Model'
                ],
                [
                    'id' => '67890',
                    'vin' => 'VIN987654321098765',
                    'make' => 'Another Make',
                    'model' => 'Another Model'
                ]
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->getList($accessToken);
        
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertCount(2, $result->getData());
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testGetListReturnsErrorResponseOnFailure(): void
    {
        $accessToken = 'invalid-token';
        
        $serviceResponse = new WSResponse(Response::HTTP_UNAUTHORIZED, [
            'error' => [
                'message' => 'Invalid access token'
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->getList($accessToken);
        
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Invalid access token', $result->getMessage());
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
    }

    public function testUpdateVehicleReturnsSuccessfulResponse(): void
    {
        $data = [
            'params' => [
                'vin' => 'VIN123456789012345'
            ],
            'accessToken' => 'test-token',
            'vehicleId' => '12345'
        ];
        
        $serviceResponse = new WSResponse(Response::HTTP_OK, [
            'success' => [
                'id' => '12345',
                'vin' => 'VIN123456789012345',
                'make' => 'Updated Make',
                'model' => 'Updated Model'
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('update')
            ->with($data)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->update($data);
        
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([
            'id' => '12345',
            'vin' => 'VIN123456789012345',
            'make' => 'Updated Make',
            'model' => 'Updated Model'
        ], $result->getData());
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testDeleteVehicleReturnsSuccessfulResponse(): void
    {
        $data = [
            'accessToken' => 'test-token',
            'vehicleId' => '12345'
        ];
        
        $serviceResponse = new WSResponse(Response::HTTP_OK, [
            'success' => [
                'message' => 'Vehicle deleted successfully'
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->delete($data);
        
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals([
            'message' => 'Vehicle deleted successfully'
        ], $result->getData());
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
    }

    public function testDeleteVehicleReturnsErrorResponseOnFailure(): void
    {
        $data = [
            'accessToken' => 'test-token',
            'vehicleId' => 'invalid-id'
        ];
        
        $serviceResponse = new WSResponse(Response::HTTP_NOT_FOUND, [
            'error' => [
                'message' => 'Vehicle not found'
            ]
        ]);
        
        $this->service->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn($serviceResponse);
        
        $result = $this->manager->delete($data);
        
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Vehicle not found', $result->getMessage());
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
    }
}
