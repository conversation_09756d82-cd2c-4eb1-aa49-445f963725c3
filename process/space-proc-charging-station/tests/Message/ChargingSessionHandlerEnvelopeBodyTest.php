<?php

namespace App\Tests\Message;

use App\Message\ChargingSessionHandlerEnvelopeBody;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;

class ChargingSessionHandlerEnvelopeBodyTest extends TestCase
{
    /**
     * Test constructor and getters
     */
    public function testConstructorAndGetters(): void
    {
        $type = 'session';
        $message = ['id' => '123', 'status' => 'active'];
        
        $envelopeBody = new ChargingSessionHandlerEnvelopeBody($type, $message);
        
        $this->assertEquals($type, $envelopeBody->getType());
        $this->assertEquals($message, $envelopeBody->getMessage());
    }
    
    /**
     * Test setters
     */
    public function testSetters(): void
    {
        $initialType = 'session';
        $initialMessage = ['id' => '123'];
        
        $envelopeBody = new ChargingSessionHandlerEnvelopeBody($initialType, $initialMessage);
        
        $newType = 'cdr';
        $newMessage = ['id' => '456', 'status' => 'completed'];
        
        $envelopeBody->setType($newType);
        $envelopeBody->setMessage($newMessage);
        
        $this->assertEquals($newType, $envelopeBody->getType());
        $this->assertEquals($newMessage, $envelopeBody->getMessage());
    }
    
    /**
     * Test validation with null message
     */
    public function testValidateEnvelopeBodyWithNullMessage(): void
    {
        $envelopeBody = new ChargingSessionHandlerEnvelopeBody('session', null);
        
        // Create mocks for validation context
        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('message')
            ->willReturnSelf();
        $violationBuilder->expects($this->once())
            ->method('addViolation')
            ->willReturn(null);
            
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->once())
            ->method('buildViolation')
            ->with('The message should not be empty and it should be array.')
            ->willReturn($violationBuilder);
            
        // Call the validation method
        $envelopeBody->validateEnvelopeBody($context);
    }
    
    /**
     * Test validation with empty type
     */
    public function testValidateEnvelopeBodyWithEmptyType(): void
    {
        $envelopeBody = new ChargingSessionHandlerEnvelopeBody('', ['id' => '123']);
        
        // Create mocks for validation context
        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('type')
            ->willReturnSelf();
        $violationBuilder->expects($this->once())
            ->method('addViolation')
            ->willReturn(null);
            
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->once())
            ->method('buildViolation')
            ->with('The type should not be empty and it should be session or cdr.')
            ->willReturn($violationBuilder);
            
        // Call the validation method
        $envelopeBody->validateEnvelopeBody($context);
    }
    
    /**
     * Test validation with invalid type
     */
    public function testValidateEnvelopeBodyWithInvalidType(): void
    {
        $envelopeBody = new ChargingSessionHandlerEnvelopeBody('invalid', ['id' => '123']);
        
        // Create mocks for validation context
        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $violationBuilder->expects($this->once())
            ->method('atPath')
            ->with('type')
            ->willReturnSelf();
        $violationBuilder->expects($this->once())
            ->method('addViolation')
            ->willReturn(null);
            
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->once())
            ->method('buildViolation')
            ->with('The type should not be empty and it should be session or cdr.')
            ->willReturn($violationBuilder);
            
        // Call the validation method
        $envelopeBody->validateEnvelopeBody($context);
    }
    
    /**
     * Test validation with valid data
     */
    public function testValidateEnvelopeBodyWithValidData(): void
    {
        $envelopeBody = new ChargingSessionHandlerEnvelopeBody('session', ['id' => '123']);
        
        // Create mock for validation context
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->never())
            ->method('buildViolation');
            
        // Call the validation method
        $envelopeBody->validateEnvelopeBody($context);
    }
}
