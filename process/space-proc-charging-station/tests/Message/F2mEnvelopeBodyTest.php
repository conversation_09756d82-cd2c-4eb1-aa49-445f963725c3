<?php

namespace App\Tests\Message;

use App\Message\F2mEnvelopeBody;
use App\Message\Field\DataField;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Validator\Validation;

class F2mEnvelopeBodyTest extends TestCase
{
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);
    }

    /**
     * Test constructor and getters
     */
    public function testConstructorAndGetters(): void
    {
        $event = 'test_event';
        $dataField = new DataField('user123', 'VIN123456789');

        $envelopeBody = new F2mEnvelopeBody($event, $dataField);

        $this->assertEquals($event, $envelopeBody->getEvent());
        $this->assertSame($dataField, $envelopeBody->getData());
    }

    /**
     * Test serialization and deserialization
     */
    public function testSerializationAndDeserialization(): void
    {
        $event = 'test_event';
        $dataField = new DataField('user123', 'VIN123456789');

        $envelopeBody = new F2mEnvelopeBody($event, $dataField);

        // Serialize
        $json = $this->serializer->serialize($envelopeBody, 'json');

        // Deserialize
        $deserializedEnvelopeBody = $this->serializer->deserialize($json, F2mEnvelopeBody::class, 'json');

        // Assert
        $this->assertEquals($event, $deserializedEnvelopeBody->getEvent());
        $this->assertEquals($dataField->getUserId(), $deserializedEnvelopeBody->getData()->getUserId());
        $this->assertEquals($dataField->getVin(), $deserializedEnvelopeBody->getData()->getVin());
    }

    /**
     * Test with valid data
     */
    public function testValidData(): void
    {
        $event = 'test_event';
        $dataField = new DataField('user123', 'VIN123456789');

        $envelopeBody = new F2mEnvelopeBody($event, $dataField);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($envelopeBody);

        $this->assertCount(0, $violations);
    }

    /**
     * Test with null data field
     */
    public function testNullDataField(): void
    {
        $event = 'test_event';
        $dataField = null;

        $envelopeBody = new F2mEnvelopeBody($event, $dataField);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($envelopeBody);

        $this->assertCount(1, $violations);
        $this->assertEquals('data', $violations[0]->getPropertyPath());
    }

    /**
     * Test with empty event
     */
    public function testEmptyEvent(): void
    {
        $event = '';
        $dataField = new DataField('user123', 'VIN123456789');

        $envelopeBody = new F2mEnvelopeBody($event, $dataField);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($envelopeBody);

        $this->assertCount(1, $violations);
        $this->assertEquals('event', $violations[0]->getPropertyPath());
    }

    /**
     * Test serialization format
     */
    public function testSerializationFormat(): void
    {
        // Create a test object
        $dataField = new DataField('user123', 'VIN123456789');
        $envelopeBody = new F2mEnvelopeBody('test_event', $dataField);

        // Serialize to JSON
        $json = $this->serializer->serialize($envelopeBody, 'json');
        $data = json_decode($json, true);

        // Debug the actual structure
        $this->assertIsArray($data);
        $this->assertArrayHasKey('event', $data);
        $this->assertEquals('test_event', $data['event']);
        $this->assertArrayHasKey('data', $data);
        $this->assertIsArray($data['data']);
        $this->assertEquals('user123', $data['data']['userId']);
        $this->assertEquals('VIN123456789', $data['data']['vin']);
    }
}
