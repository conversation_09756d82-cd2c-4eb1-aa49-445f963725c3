<?php

namespace App\Tests\Message\Field;

use App\Message\Field\DataField;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Validator\Validation;

class DataFieldTest extends TestCase
{
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);
    }

    /**
     * Test constructor and getters
     */
    public function testConstructorAndGetters(): void
    {
        $userId = 'user123';
        $vin = 'VIN123456789';

        $dataField = new DataField($userId, $vin);

        $this->assertEquals($userId, $dataField->getUserId());
        $this->assertEquals($vin, $dataField->getVin());
    }

    /**
     * Test serialization and deserialization
     */
    public function testSerializationAndDeserialization(): void
    {
        $userId = 'user123';
        $vin = 'VIN123456789';

        $dataField = new DataField($userId, $vin);

        // Serialize
        $json = $this->serializer->serialize($dataField, 'json');

        // Deserialize
        $deserializedDataField = $this->serializer->deserialize($json, DataField::class, 'json');

        // Assert
        $this->assertEquals($userId, $deserializedDataField->getUserId());
        $this->assertEquals($vin, $deserializedDataField->getVin());
    }

    /**
     * Test with valid data
     */
    public function testValidData(): void
    {
        $userId = 'user123';
        $vin = 'VIN123456789';

        $dataField = new DataField($userId, $vin);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($dataField);

        $this->assertCount(0, $violations);
    }

    /**
     * Test with empty userId
     */
    public function testEmptyUserId(): void
    {
        $userId = '';
        $vin = 'VIN123456789';

        $dataField = new DataField($userId, $vin);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($dataField);

        $this->assertCount(1, $violations);
        $this->assertEquals('userId', $violations[0]->getPropertyPath());
    }

    /**
     * Test with empty vin
     */
    public function testEmptyVin(): void
    {
        $userId = 'user123';
        $vin = '';

        $dataField = new DataField($userId, $vin);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($dataField);

        $this->assertCount(1, $violations);
        $this->assertEquals('vin', $violations[0]->getPropertyPath());
    }

    /**
     * Test with both empty fields
     */
    public function testBothEmptyFields(): void
    {
        $userId = '';
        $vin = '';

        $dataField = new DataField($userId, $vin);

        $validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $violations = $validator->validate($dataField);

        $this->assertCount(2, $violations);
    }

    /**
     * Test with JSON data
     */
    public function testDeserializeFromJson(): void
    {
        $json = json_encode([
            'userId' => 'user123',
            'vin' => 'VIN123456789'
        ]);

        $deserializedDataField = $this->serializer->deserialize($json, DataField::class, 'json');

        $this->assertEquals('user123', $deserializedDataField->getUserId());
        $this->assertEquals('VIN123456789', $deserializedDataField->getVin());
    }
}
