<?php

namespace App\Tests\Message;

use App\Message\ChargingStationCallback;
use App\Message\SqsMessageDecoder;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Serializer\SerializerInterface;

class SqsMessageDecoderTest extends TestCase
{
    private SerializerInterface $serializer;
    private LoggerInterface $logger;
    private SqsMessageDecoder $decoder;

    protected function setUp(): void
    {
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->decoder = new SqsMessageDecoder($this->serializer);
        $this->decoder->setLogger($this->logger);
    }

    public function testDecodeSuccess(): void
    {

        $encodedEnvelope = [
            'body' => '{"MessageBody": {"result": "ACCEPTED"}}',
            'headers' => [
                'type' => ChargingStationCallback::class,
                'action' => 'someAction',
                'internalId' => 'someInternalId',
                'userId' => 'someUserId',
                'sessionId' => 'someSessionId',
            ],
        ];

        $expectedBody = json_encode([
            'result' => 'ACCEPTED',
            'action' => 'someAction',
            'internalId' => 'someInternalId',
            'userId' => 'someUserId',
            'sessionId' => 'someSessionId',
        ]);

        $this->serializer->expects($this->once())
            ->method('deserialize')
            ->with($expectedBody, ChargingStationCallback::class, 'json', $this->anything())
            ->willReturn(new ChargingStationCallback(
                'someInternalId',
                'someUserId',
                'someSessionId',
                'someAction',
                'ACCEPTED'
            ));

        $result = $this->decoder->decode($encodedEnvelope);

        $this->assertInstanceOf(Envelope::class, $result);
        $this->assertInstanceOf(ChargingStationCallback::class, $result->getMessage());
    }

    public function testDecodeWithException(): void
    {
        $encodedEnvelope = [
            'body' => json_encode(['action' => 'someAction', 'internalId' => 'someInternalId', 'userId' => 'someUserId', 'sessionId' => 'someSessionId']),
            'headers' => [
                'type' => ChargingStationCallback::class,
                'action' => 'someAction',
                'internalId' => 'someInternalId',
                'userId' => 'someUserId',
                'sessionId' => 'someSessionId',
            ],
        ];

        $this->serializer->expects($this->once())
            ->method('deserialize')
            ->willThrowException(new \Exception('An error occurred'));

        // We don't need to check the exact exception message since it will be dynamic
        // Just verify that error is logged with the correct parameters
        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                'Error processing SQS message',
                $this->callback(function ($params) use ($encodedEnvelope) {
                    // Check that the exception key exists and encodedEnvelope matches
                    return isset($params['exception']) &&
                           isset($params['encodedEnvelope']) &&
                           $params['encodedEnvelope']['headers'] === $encodedEnvelope['headers'];
                })
            );

        $this->expectException(\Exception::class);
        $this->decoder->decode($encodedEnvelope);
    }
}
