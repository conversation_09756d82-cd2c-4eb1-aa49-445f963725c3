<?php

namespace App\Tests\MessageHandler;

use App\Message\ChargingSessionHandlerEnvelopeBody;
use App\MessageHandler\ChargingSessionHandler;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingSessionHandlerTest extends TestCase
{
    private $validator;
    private $logger;
    private $userService;
    private $handler;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->userService = $this->createMock(UserService::class);

        $this->handler = new ChargingSessionHandler(
            $this->validator,
            $this->logger,
            $this->userService
        );
    }

    public function testInvokeWithInvalidEnvelopeBody(): void
    {
        $envelopeBody = $this->createMock(ChargingSessionHandlerEnvelopeBody::class);

        $violation = $this->createMock(ConstraintViolation::class);
        $violation->method('getPropertyPath')->willReturn('type');
        $violation->method('getMessage')->willReturn('This value should not be blank.');

        $violations = new ConstraintViolationList([$violation]);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($envelopeBody)
            ->willReturn($violations);

        $envelopeBody->expects($this->once())
            ->method('getMessage')
            ->willReturn(['id' => '123']);

        $this->expectException(UnrecoverableMessageHandlingException::class);

        ($this->handler)($envelopeBody);
    }

    public function testInvokeWithInvalidMessageFields(): void
    {
        $envelopeBody = $this->createMock(ChargingSessionHandlerEnvelopeBody::class);

        // Configure validator to pass first validation and fail second validation
        $this->validator->expects($this->exactly(2))
            ->method('validate')
            ->willReturnOnConsecutiveCalls(
                new ConstraintViolationList(),
                $this->returnCallback(function() {
                    $violation = $this->createMock(ConstraintViolation::class);
                    $violation->method('getPropertyPath')->willReturn('status');
                    $violation->method('getMessage')->willReturn('This value should not be blank.');
                    return new ConstraintViolationList([$violation]);
                })
            );

        $message = [
            'id' => '123',
            'userId' => 'user123',
            'status' => '' // Empty status will fail validation
        ];

        $envelopeBody->expects($this->atLeastOnce())
            ->method('getMessage')
            ->willReturn($message);

        $this->expectException(UnrecoverableMessageHandlingException::class);

        ($this->handler)($envelopeBody);
    }

    public function testInvokeWithUserFoundBySessionId(): void
    {
        $envelopeBody = $this->createMock(ChargingSessionHandlerEnvelopeBody::class);

        // Configure validator to pass all validations
        $this->validator->expects($this->exactly(2))
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        $message = [
            'id' => 'session123',
            'userId' => 'user123',
            'status' => 'active'
        ];

        $envelopeBody->expects($this->atLeastOnce())
            ->method('getMessage')
            ->willReturn($message);

        // Mock user response
        $userResponse = $this->createMock(\App\Helper\WSResponse::class);
        $userResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $userResponse->method('getData')->willReturn(json_encode([
            'documents' => [
                ['userId' => 'user123']
            ]
        ]));

        $this->userService->expects($this->once())
            ->method('getUserByF2MUserId')
            ->with('user123', 'session123')
            ->willReturn($userResponse);

        // Mock status update response
        $statusUpdateResponse = $this->createMock(\App\Helper\WSResponse::class);
        $statusUpdateResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $statusUpdateResponse->method('getData')->willReturn('{"status": "updated"}');

        $this->userService->expects($this->once())
            ->method('updateUserRemoteCommandStatus')
            ->with('user123', 'session123', 'active')
            ->willReturn($statusUpdateResponse);

        // No exception should be thrown
        ($this->handler)($envelopeBody);
    }

    public function testInvokeWithUserFoundByOtherIds(): void
    {
        $envelopeBody = $this->createMock(ChargingSessionHandlerEnvelopeBody::class);

        // Configure validator to pass all validations
        $this->validator->expects($this->exactly(3))
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        $message = [
            'id' => 'session123',
            'userId' => 'user123',
            'status' => 'active',
            'locationId' => 'location123',
            'evseId' => 'evse123',
            'connectorId' => 'connector123'
        ];

        $envelopeBody->expects($this->atLeastOnce())
            ->method('getMessage')
            ->willReturn($message);

        // User not found by session ID
        $userResponseBySessionId = $this->createMock(\App\Helper\WSResponse::class);
        $userResponseBySessionId->method('getCode')->willReturn(Response::HTTP_OK);
        $userResponseBySessionId->method('getData')->willReturn(json_encode([
            'documents' => []
        ]));

        $this->userService->expects($this->once())
            ->method('getUserByF2MUserId')
            ->with('user123', 'session123')
            ->willReturn($userResponseBySessionId);

        // User found by other IDs
        $userResponseByOtherIds = $this->createMock(\App\Helper\WSResponse::class);
        $userResponseByOtherIds->method('getCode')->willReturn(Response::HTTP_OK);
        $userResponseByOtherIds->method('getData')->willReturn(json_encode([
            'documents' => [
                ['userId' => 'user123']
            ]
        ]));

        $this->userService->expects($this->once())
            ->method('getUserByOtherIds')
            ->with('user123', 'location123', 'evse123', 'connector123')
            ->willReturn($userResponseByOtherIds);

        // Mock status update response
        $statusUpdateResponse = $this->createMock(\App\Helper\WSResponse::class);
        $statusUpdateResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $statusUpdateResponse->method('getData')->willReturn('{"status": "updated"}');

        $this->userService->expects($this->once())
            ->method('updateUserRemoteCommandStatusUsingOtherIds')
            ->with('user123', 'location123', 'evse123', 'connector123', 'session123', 'active')
            ->willReturn($statusUpdateResponse);

        // No exception should be thrown
        ($this->handler)($envelopeBody);
    }

    public function testInvokeWithUserNotFound(): void
    {
        $envelopeBody = $this->createMock(ChargingSessionHandlerEnvelopeBody::class);

        // Configure validator to pass all validations
        $this->validator->expects($this->exactly(3))
            ->method('validate')
            ->willReturn(new ConstraintViolationList());

        $message = [
            'id' => 'session123',
            'userId' => 'user123',
            'status' => 'active',
            'locationId' => 'location123',
            'evseId' => 'evse123',
            'connectorId' => 'connector123'
        ];

        $envelopeBody->expects($this->atLeastOnce())
            ->method('getMessage')
            ->willReturn($message);

        // User not found by session ID
        $userResponseBySessionId = $this->createMock(\App\Helper\WSResponse::class);
        $userResponseBySessionId->method('getCode')->willReturn(Response::HTTP_OK);
        $userResponseBySessionId->method('getData')->willReturn(json_encode([
            'documents' => []
        ]));

        $this->userService->expects($this->once())
            ->method('getUserByF2MUserId')
            ->with('user123', 'session123')
            ->willReturn($userResponseBySessionId);

        // User not found by other IDs
        $userResponseByOtherIds = $this->createMock(\App\Helper\WSResponse::class);
        $userResponseByOtherIds->method('getCode')->willReturn(Response::HTTP_OK);
        $userResponseByOtherIds->method('getData')->willReturn(json_encode([
            'documents' => []
        ]));

        $this->userService->expects($this->once())
            ->method('getUserByOtherIds')
            ->with('user123', 'location123', 'evse123', 'connector123')
            ->willReturn($userResponseByOtherIds);

        // No exception should be thrown, handler should complete normally
        ($this->handler)($envelopeBody);
    }
}
