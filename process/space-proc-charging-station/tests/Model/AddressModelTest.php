<?php

namespace App\Tests\Model;

use App\Model\AddressModel;
use PHPUnit\Framework\TestCase;

class AddressModelTest extends TestCase
{
    private AddressModel $addressModel;

    protected function setUp(): void
    {
        $this->addressModel = new AddressModel();
    }

    public function testCountryGetterAndSetter(): void
    {
        $country = 'Germany';
        $this->addressModel->setCountry($country);
        $this->assertEquals($country, $this->addressModel->getCountry());
    }

    public function testCountryCodeGetterAndSetter(): void
    {
        $countryCode = 'DE';
        $this->addressModel->setCountryCode($countryCode);
        $this->assertEquals($countryCode, $this->addressModel->getCountryCode());
    }

    public function testCountryCodeISO3GetterAndSetter(): void
    {
        $countryCodeISO3 = 'DEU';
        $this->addressModel->setCountryCodeISO3($countryCodeISO3);
        $this->assertEquals($countryCodeISO3, $this->addressModel->getCountryCodeISO3());
    }

    public function testCountrySecondarySubdivisionGetterAndSetter(): void
    {
        $countrySecondarySubdivision = 'Munich';
        $this->addressModel->setCountrySecondarySubdivision($countrySecondarySubdivision);
        $this->assertEquals($countrySecondarySubdivision, $this->addressModel->getCountrySecondarySubdivision());
    }

    public function testCountrySubdivisionGetterAndSetter(): void
    {
        $countrySubdivision = 'Bavaria';
        $this->addressModel->setCountrySubdivision($countrySubdivision);
        $this->assertEquals($countrySubdivision, $this->addressModel->getCountrySubdivision());
    }

    public function testCountrySubdivisionNameGetterAndSetter(): void
    {
        $countrySubdivisionName = 'Bayern';
        $this->addressModel->setCountrySubdivisionName($countrySubdivisionName);
        $this->assertEquals($countrySubdivisionName, $this->addressModel->getCountrySubdivisionName());
    }

    public function testCountryTertiarySubdivisionGetterAndSetter(): void
    {
        $countryTertiarySubdivision = 'Schwabing';
        $this->addressModel->setCountryTertiarySubdivision($countryTertiarySubdivision);
        $this->assertEquals($countryTertiarySubdivision, $this->addressModel->getCountryTertiarySubdivision());
    }

    public function testExtendedPostalCodeGetterAndSetter(): void
    {
        $extendedPostalCode = '80331-1234';
        $this->addressModel->setExtendedPostalCode($extendedPostalCode);
        $this->assertEquals($extendedPostalCode, $this->addressModel->getExtendedPostalCode());
    }

    public function testFreeformAddressGetterAndSetter(): void
    {
        $freeformAddress = 'Leopoldstr. 123, 80331 Munich, Germany';
        $this->addressModel->setFreeformAddress($freeformAddress);
        $this->assertEquals($freeformAddress, $this->addressModel->getFreeformAddress());
    }

    public function testLocalNameGetterAndSetter(): void
    {
        $localName = 'München';
        $this->addressModel->setLocalName($localName);
        $this->assertEquals($localName, $this->addressModel->getLocalName());
    }

    public function testMunicipalityGetterAndSetter(): void
    {
        $municipality = 'Munich';
        $this->addressModel->setMunicipality($municipality);
        $this->assertEquals($municipality, $this->addressModel->getMunicipality());
    }

    public function testPostalCodeGetterAndSetter(): void
    {
        $postalCode = '80331';
        $this->addressModel->setPostalCode($postalCode);
        $this->assertEquals($postalCode, $this->addressModel->getPostalCode());
    }

    public function testStreetNameGetterAndSetter(): void
    {
        $streetName = 'Leopoldstr.';
        $this->addressModel->setStreetName($streetName);
        $this->assertEquals($streetName, $this->addressModel->getStreetName());
    }

    public function testStreetNumberGetterAndSetter(): void
    {
        $streetNumber = '123';
        $this->addressModel->setStreetNumber($streetNumber);
        $this->assertEquals($streetNumber, $this->addressModel->getStreetNumber());
    }

    public function testFluidInterface(): void
    {
        $result = $this->addressModel
            ->setCountry('Germany')
            ->setCountryCode('DE')
            ->setStreetName('Leopoldstr.')
            ->setStreetNumber('123');
        
        $this->assertInstanceOf(AddressModel::class, $result);
    }
}
