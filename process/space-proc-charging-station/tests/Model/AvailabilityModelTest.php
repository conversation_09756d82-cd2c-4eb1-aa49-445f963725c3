<?php

namespace App\Tests\Model;

use App\Model\AvailabilityModel;
use PHPUnit\Framework\TestCase;

class AvailabilityModelTest extends TestCase
{
    private AvailabilityModel $availabilityModel;

    protected function setUp(): void
    {
        $this->availabilityModel = new AvailabilityModel();
    }

    public function testAvailableGetterAndSetter(): void
    {
        $available = 5;
        $this->availabilityModel->setAvailable($available);
        $this->assertEquals($available, $this->availabilityModel->getAvailable());
    }

    public function testOccupiedGetterAndSetter(): void
    {
        $occupied = 3;
        $this->availabilityModel->setOccupied($occupied);
        $this->assertEquals($occupied, $this->availabilityModel->getOccupied());
    }

    public function testOutOfServiceGetterAndSetter(): void
    {
        $outOfService = 2;
        $this->availabilityModel->setOutOfService($outOfService);
        $this->assertEquals($outOfService, $this->availabilityModel->getOutOfService());
    }

    public function testReservedGetterAndSetter(): void
    {
        $reserved = 1;
        $this->availabilityModel->setReserved($reserved);
        $this->assertEquals($reserved, $this->availabilityModel->getReserved());
    }

    public function testUnknownGetterAndSetter(): void
    {
        $unknown = 0;
        $this->availabilityModel->setUnknown($unknown);
        $this->assertEquals($unknown, $this->availabilityModel->getUnknown());
    }

    public function testFluidInterface(): void
    {
        $result = $this->availabilityModel
            ->setAvailable(5)
            ->setOccupied(3)
            ->setOutOfService(2)
            ->setReserved(1)
            ->setUnknown(0);
        
        $this->assertInstanceOf(AvailabilityModel::class, $result);
    }

    public function testZeroValues(): void
    {
        $this->availabilityModel
            ->setAvailable(0)
            ->setOccupied(0)
            ->setOutOfService(0)
            ->setReserved(0)
            ->setUnknown(0);
        
        $this->assertEquals(0, $this->availabilityModel->getAvailable());
        $this->assertEquals(0, $this->availabilityModel->getOccupied());
        $this->assertEquals(0, $this->availabilityModel->getOutOfService());
        $this->assertEquals(0, $this->availabilityModel->getReserved());
        $this->assertEquals(0, $this->availabilityModel->getUnknown());
    }

    public function testNegativeValues(): void
    {
        $this->availabilityModel
            ->setAvailable(-1)
            ->setOccupied(-2)
            ->setOutOfService(-3)
            ->setReserved(-4)
            ->setUnknown(-5);
        
        $this->assertEquals(-1, $this->availabilityModel->getAvailable());
        $this->assertEquals(-2, $this->availabilityModel->getOccupied());
        $this->assertEquals(-3, $this->availabilityModel->getOutOfService());
        $this->assertEquals(-4, $this->availabilityModel->getReserved());
        $this->assertEquals(-5, $this->availabilityModel->getUnknown());
    }

    public function testLargeValues(): void
    {
        $this->availabilityModel
            ->setAvailable(PHP_INT_MAX)
            ->setOccupied(PHP_INT_MAX)
            ->setOutOfService(PHP_INT_MAX)
            ->setReserved(PHP_INT_MAX)
            ->setUnknown(PHP_INT_MAX);
        
        $this->assertEquals(PHP_INT_MAX, $this->availabilityModel->getAvailable());
        $this->assertEquals(PHP_INT_MAX, $this->availabilityModel->getOccupied());
        $this->assertEquals(PHP_INT_MAX, $this->availabilityModel->getOutOfService());
        $this->assertEquals(PHP_INT_MAX, $this->availabilityModel->getReserved());
        $this->assertEquals(PHP_INT_MAX, $this->availabilityModel->getUnknown());
    }
}
