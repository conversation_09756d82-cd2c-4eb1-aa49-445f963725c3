<?php

namespace App\Tests\Model;

use App\Model\ChargeTypeAvailabilityModel;
use PHPUnit\Framework\TestCase;

class ChargeTypeAvailabilityModelTest extends TestCase
{
    private ChargeTypeAvailabilityModel $chargeTypeAvailabilityModel;

    protected function setUp(): void
    {
        $this->chargeTypeAvailabilityModel = new ChargeTypeAvailabilityModel();
    }

    public function testFastChargeGetterAndSetter(): void
    {
        $fastCharge = 3;
        $this->chargeTypeAvailabilityModel->setFastCharge($fastCharge);
        $this->assertEquals($fastCharge, $this->chargeTypeAvailabilityModel->getFastCharge());
    }

    public function testRegularChargeGetterAndSetter(): void
    {
        $regularCharge = 5;
        $this->chargeTypeAvailabilityModel->setRegularCharge($regularCharge);
        $this->assertEquals($regularCharge, $this->chargeTypeAvailabilityModel->getRegularCharge());
    }

    public function testSlowChargeGetterAndSetter(): void
    {
        $slowCharge = 2;
        $this->chargeTypeAvailabilityModel->setSlowCharge($slowCharge);
        $this->assertEquals($slowCharge, $this->chargeTypeAvailabilityModel->getSlowCharge());
    }

    public function testUnknownGetterAndSetter(): void
    {
        $unknown = 1;
        $this->chargeTypeAvailabilityModel->setUnknown($unknown);
        $this->assertEquals($unknown, $this->chargeTypeAvailabilityModel->getUnknown());
    }

    public function testFluidInterface(): void
    {
        $result = $this->chargeTypeAvailabilityModel
            ->setFastCharge(3)
            ->setRegularCharge(5)
            ->setSlowCharge(2)
            ->setUnknown(1);
        
        $this->assertInstanceOf(ChargeTypeAvailabilityModel::class, $result);
    }

    public function testZeroValues(): void
    {
        $this->chargeTypeAvailabilityModel
            ->setFastCharge(0)
            ->setRegularCharge(0)
            ->setSlowCharge(0)
            ->setUnknown(0);
        
        $this->assertEquals(0, $this->chargeTypeAvailabilityModel->getFastCharge());
        $this->assertEquals(0, $this->chargeTypeAvailabilityModel->getRegularCharge());
        $this->assertEquals(0, $this->chargeTypeAvailabilityModel->getSlowCharge());
        $this->assertEquals(0, $this->chargeTypeAvailabilityModel->getUnknown());
    }

    public function testNegativeValues(): void
    {
        $this->chargeTypeAvailabilityModel
            ->setFastCharge(-1)
            ->setRegularCharge(-2)
            ->setSlowCharge(-3)
            ->setUnknown(-4);
        
        $this->assertEquals(-1, $this->chargeTypeAvailabilityModel->getFastCharge());
        $this->assertEquals(-2, $this->chargeTypeAvailabilityModel->getRegularCharge());
        $this->assertEquals(-3, $this->chargeTypeAvailabilityModel->getSlowCharge());
        $this->assertEquals(-4, $this->chargeTypeAvailabilityModel->getUnknown());
    }

    public function testLargeValues(): void
    {
        $this->chargeTypeAvailabilityModel
            ->setFastCharge(PHP_INT_MAX)
            ->setRegularCharge(PHP_INT_MAX)
            ->setSlowCharge(PHP_INT_MAX)
            ->setUnknown(PHP_INT_MAX);
        
        $this->assertEquals(PHP_INT_MAX, $this->chargeTypeAvailabilityModel->getFastCharge());
        $this->assertEquals(PHP_INT_MAX, $this->chargeTypeAvailabilityModel->getRegularCharge());
        $this->assertEquals(PHP_INT_MAX, $this->chargeTypeAvailabilityModel->getSlowCharge());
        $this->assertEquals(PHP_INT_MAX, $this->chargeTypeAvailabilityModel->getUnknown());
    }
}
