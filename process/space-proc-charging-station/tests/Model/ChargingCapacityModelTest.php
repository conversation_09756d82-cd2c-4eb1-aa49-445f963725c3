<?php

namespace App\Tests\Model;

use App\Model\ChargingCapacityModel;
use PHPUnit\Framework\TestCase;

class ChargingCapacityModelTest extends TestCase
{
    private ChargingCapacityModel $chargingCapacityModel;

    protected function setUp(): void
    {
        $this->chargingCapacityModel = new ChargingCapacityModel();
    }

    public function testChargingModeGetterAndSetter(): void
    {
        $chargingMode = 'fast';
        $this->chargingCapacityModel->setChargingMode($chargingMode);
        $this->assertEquals($chargingMode, $this->chargingCapacityModel->getChargingMode());
    }

    public function testPowerKwGetterAndSetter(): void
    {
        $powerKw = 150.0;
        $this->chargingCapacityModel->setPowerKw($powerKw);
        $this->assertEquals($powerKw, $this->chargingCapacityModel->getPowerKw());
    }

    public function testTypeGetterAndSetter(): void
    {
        $type = 'DC';
        $this->chargingCapacityModel->setType($type);
        $this->assertEquals($type, $this->chargingCapacityModel->getType());
    }

    public function testNullValues(): void
    {
        $this->chargingCapacityModel->setChargingMode(null);
        $this->chargingCapacityModel->setPowerKw(null);
        $this->chargingCapacityModel->setType(null);
        
        $this->assertNull($this->chargingCapacityModel->getChargingMode());
        $this->assertNull($this->chargingCapacityModel->getPowerKw());
        $this->assertNull($this->chargingCapacityModel->getType());
    }

    public function testFluidInterface(): void
    {
        $result = $this->chargingCapacityModel
            ->setChargingMode('fast')
            ->setPowerKw(150.0)
            ->setType('DC');
        
        $this->assertInstanceOf(ChargingCapacityModel::class, $result);
    }

    public function testZeroPowerKw(): void
    {
        $powerKw = 0.0;
        $this->chargingCapacityModel->setPowerKw($powerKw);
        $this->assertEquals($powerKw, $this->chargingCapacityModel->getPowerKw());
    }

    public function testNegativePowerKw(): void
    {
        $powerKw = -22.0;
        $this->chargingCapacityModel->setPowerKw($powerKw);
        $this->assertEquals($powerKw, $this->chargingCapacityModel->getPowerKw());
    }

    public function testDecimalPrecision(): void
    {
        $powerKw = 22.123456789;
        $this->chargingCapacityModel->setPowerKw($powerKw);
        $this->assertEquals($powerKw, $this->chargingCapacityModel->getPowerKw());
    }

    public function testEmptyStringValues(): void
    {
        $this->chargingCapacityModel->setChargingMode('');
        $this->chargingCapacityModel->setType('');
        
        $this->assertEquals('', $this->chargingCapacityModel->getChargingMode());
        $this->assertEquals('', $this->chargingCapacityModel->getType());
    }
}
