<?php

namespace App\Tests\Model;

use App\Model\ConnectorModel;
use App\Model\AvailabilityModel;
use App\Model\PowerLevelModel;
use PHPUnit\Framework\TestCase;

class ConnectorModelTest extends TestCase
{
    private ConnectorModel $connectorModel;
    private AvailabilityModel $availabilityModel;
    private PowerLevelModel $powerLevelModel;

    protected function setUp(): void
    {
        $this->connectorModel = new ConnectorModel();
        $this->availabilityModel = new AvailabilityModel();
        $this->powerLevelModel = new PowerLevelModel();
    }

    public function testAvailabilityGetterAndSetter(): void
    {
        $this->availabilityModel
            ->setAvailable(5)
            ->setOccupied(3)
            ->setOutOfService(2)
            ->setReserved(1)
            ->setUnknown(0);
        
        $this->connectorModel->setAvailability($this->availabilityModel);
        $this->assertSame($this->availabilityModel, $this->connectorModel->getAvailability());
    }

    public function testCompatibleGetterAndSetter(): void
    {
        $compatible = true;
        $this->connectorModel->setCompatible($compatible);
        $this->assertEquals($compatible, $this->connectorModel->getCompatible());
    }

    public function testPowerLevelGetterAndSetter(): void
    {
        $this->connectorModel->setPowerLevel($this->powerLevelModel);
        $this->assertSame($this->powerLevelModel, $this->connectorModel->getPowerLevel());
    }

    public function testTotalGetterAndSetter(): void
    {
        $total = 10;
        $this->connectorModel->setTotal($total);
        $this->assertEquals($total, $this->connectorModel->getTotal());
    }

    public function testTypeGetterAndSetter(): void
    {
        $type = 'CCS';
        $this->connectorModel->setType($type);
        $this->assertEquals($type, $this->connectorModel->getType());
    }

    public function testEvseIdGetterAndSetter(): void
    {
        $evseId = 'evse-123';
        $this->connectorModel->setEvseId($evseId);
        $this->assertEquals($evseId, $this->connectorModel->getEvseId());
    }

    public function testConnectorIdGetterAndSetter(): void
    {
        $connectorId = 'connector-456';
        $this->connectorModel->setConnectorId($connectorId);
        $this->assertEquals($connectorId, $this->connectorModel->getConnectorId());
    }

    public function testPriceTextGetterAndSetter(): void
    {
        $priceText = '0.49 EUR/kWh';
        $this->connectorModel->setPriceText($priceText);
        $this->assertEquals($priceText, $this->connectorModel->getPriceText());
    }

    public function testSpeedShortCodeGetterAndSetter(): void
    {
        $speedShortCode = 'fast';
        $this->connectorModel->setSpeedShortCode($speedShortCode);
        $this->assertEquals($speedShortCode, $this->connectorModel->getSpeedShortCode());
    }

    public function testCapabilitiesGetterAndSetter(): void
    {
        $capabilities = ['remote_start', 'remote_stop', 'reservation'];
        $this->connectorModel->setCapabilities($capabilities);
        $this->assertEquals($capabilities, $this->connectorModel->getCapabilities());
    }

    public function testNullValues(): void
    {
        $this->connectorModel->setAvailability(null);
        $this->connectorModel->setPowerLevel(null);
        $this->connectorModel->setType(null);
        $this->connectorModel->setEvseId(null);
        $this->connectorModel->setConnectorId(null);
        $this->connectorModel->setPriceText(null);
        $this->connectorModel->setSpeedShortCode(null);
        $this->connectorModel->setCapabilities(null);
        
        $this->assertNull($this->connectorModel->getAvailability());
        $this->assertNull($this->connectorModel->getPowerLevel());
        $this->assertNull($this->connectorModel->getType());
        $this->assertNull($this->connectorModel->getEvseId());
        $this->assertNull($this->connectorModel->getConnectorId());
        $this->assertNull($this->connectorModel->getPriceText());
        $this->assertNull($this->connectorModel->getSpeedShortCode());
        $this->assertNull($this->connectorModel->getCapabilities());
    }

    public function testFluidInterface(): void
    {
        $result = $this->connectorModel
            ->setAvailability($this->availabilityModel)
            ->setCompatible(true)
            ->setPowerLevel($this->powerLevelModel)
            ->setTotal(10)
            ->setType('CCS')
            ->setEvseId('evse-123')
            ->setConnectorId('connector-456')
            ->setPriceText('0.49 EUR/kWh')
            ->setSpeedShortCode('fast')
            ->setCapabilities(['remote_start']);
        
        $this->assertInstanceOf(ConnectorModel::class, $result);
    }

    public function testFalseCompatible(): void
    {
        $compatible = false;
        $this->connectorModel->setCompatible($compatible);
        $this->assertEquals($compatible, $this->connectorModel->getCompatible());
    }

    public function testZeroTotal(): void
    {
        $total = 0;
        $this->connectorModel->setTotal($total);
        $this->assertEquals($total, $this->connectorModel->getTotal());
    }

    public function testEmptyCapabilities(): void
    {
        $capabilities = [];
        $this->connectorModel->setCapabilities($capabilities);
        $this->assertEquals($capabilities, $this->connectorModel->getCapabilities());
    }
}
