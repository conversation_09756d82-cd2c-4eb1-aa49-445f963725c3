<?php

namespace App\Tests\Model;

use App\Model\CoordinateModel;
use PHPUnit\Framework\TestCase;

class CoordinateModelTest extends TestCase
{
    private CoordinateModel $coordinateModel;

    protected function setUp(): void
    {
        $this->coordinateModel = new CoordinateModel();
    }

    public function testLatGetterAndSetter(): void
    {
        $latitude = 48.1351;
        $this->coordinateModel->setLat($latitude);
        $this->assertEquals($latitude, $this->coordinateModel->getLat());
    }

    public function testLonGetterAndSetter(): void
    {
        $longitude = 11.5820;
        $this->coordinateModel->setLon($longitude);
        $this->assertEquals($longitude, $this->coordinateModel->getLon());
    }

    public function testNullValues(): void
    {
        $this->coordinateModel->setLat(null);
        $this->coordinateModel->setLon(null);
        
        $this->assertNull($this->coordinateModel->getLat());
        $this->assertNull($this->coordinateModel->getLon());
    }

    public function testFluidInterface(): void
    {
        $result = $this->coordinateModel
            ->setLat(48.1351)
            ->setLon(11.5820);
        
        $this->assertInstanceOf(CoordinateModel::class, $result);
    }

    public function testNegativeCoordinates(): void
    {
        $latitude = -33.8688;
        $longitude = -151.2093;
        
        $this->coordinateModel->setLat($latitude);
        $this->coordinateModel->setLon($longitude);
        
        $this->assertEquals($latitude, $this->coordinateModel->getLat());
        $this->assertEquals($longitude, $this->coordinateModel->getLon());
    }

    public function testZeroCoordinates(): void
    {
        $latitude = 0.0;
        $longitude = 0.0;
        
        $this->coordinateModel->setLat($latitude);
        $this->coordinateModel->setLon($longitude);
        
        $this->assertEquals($latitude, $this->coordinateModel->getLat());
        $this->assertEquals($longitude, $this->coordinateModel->getLon());
    }

    public function testDecimalPrecision(): void
    {
        $latitude = 48.13512345678;
        $longitude = 11.58201234567;
        
        $this->coordinateModel->setLat($latitude);
        $this->coordinateModel->setLon($longitude);
        
        $this->assertEquals($latitude, $this->coordinateModel->getLat());
        $this->assertEquals($longitude, $this->coordinateModel->getLon());
    }
}
