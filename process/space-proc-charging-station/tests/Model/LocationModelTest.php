<?php

namespace App\Tests\Model;

use App\Model\LocationModel;
use App\Model\AddressModel;
use App\Model\PoiModel;
use App\Model\CoordinateModel;
use PHPUnit\Framework\TestCase;

class LocationModelTest extends TestCase
{
    private LocationModel $locationModel;
    private AddressModel $addressModel;
    private PoiModel $poiModel;
    private CoordinateModel $coordinateModel;

    protected function setUp(): void
    {
        $this->locationModel = new LocationModel();
        $this->addressModel = new AddressModel();
        $this->poiModel = new PoiModel();
        $this->coordinateModel = new CoordinateModel();
    }

    public function testAddressGetterAndSetter(): void
    {
        $this->addressModel->setCountry('Germany')
            ->setMunicipality('Munich')
            ->setStreetName('Leopoldstr.')
            ->setStreetNumber('123');

        $this->locationModel->setAddress($this->addressModel);
        $this->assertSame($this->addressModel, $this->locationModel->getAddress());
    }

    public function testBrandGetterAndSetter(): void
    {
        $brand = 'ChargePoint';
        $this->locationModel->setBrand($brand);
        $this->assertEquals($brand, $this->locationModel->getBrand());
    }

    public function testConnectorsGetterAndSetter(): void
    {
        $connectors = [
            [
                'id' => 'connector-1',
                'type' => 'CCS',
                'status' => 'available'
            ],
            [
                'id' => 'connector-2',
                'type' => 'CHAdeMO',
                'status' => 'occupied'
            ]
        ];

        $this->locationModel->setConnectors($connectors);
        $this->assertEquals($connectors, $this->locationModel->getConnectors());
    }

    public function testIdGetterAndSetter(): void
    {
        $id = 'location-123';
        $this->locationModel->setId($id);
        $this->assertEquals($id, $this->locationModel->getId());
    }

    public function testLocationIdGetterAndSetter(): void
    {
        $locationId = 'loc-456';
        $this->locationModel->setLocationId($locationId);
        $this->assertEquals($locationId, $this->locationModel->getLocationId());
    }

    public function testPoiGetterAndSetter(): void
    {
        $this->poiModel->setName('Charging Station Munich Central')
            ->setAccessType('public')
            ->setTwentyFourSeven(true);

        $this->locationModel->setPoi($this->poiModel);
        $this->assertSame($this->poiModel, $this->locationModel->getPoi());
    }

    public function testPositionGetterAndSetter(): void
    {
        $this->coordinateModel->setLat(48.1351)
            ->setLon(11.5820);

        $this->locationModel->setPosition($this->coordinateModel);
        $this->assertSame($this->coordinateModel, $this->locationModel->getPosition());
    }

    public function testNullValues(): void
    {
        $this->locationModel->setBrand(null);
        $this->locationModel->setId(null);
        $this->locationModel->setLocationId(null);

        $this->assertNull($this->locationModel->getBrand());
        $this->assertNull($this->locationModel->getId());
        $this->assertNull($this->locationModel->getLocationId());
    }

    public function testFluidInterface(): void
    {
        $result = $this->locationModel
            ->setAddress($this->addressModel)
            ->setBrand('ChargePoint')
            ->setConnectors([])
            ->setId('location-123')
            ->setLocationId('loc-456')
            ->setPoi($this->poiModel)
            ->setPosition($this->coordinateModel);

        $this->assertInstanceOf(LocationModel::class, $result);
    }

    public function testEmptyConnectors(): void
    {
        $connectors = [];
        $this->locationModel->setConnectors($connectors);
        $this->assertEquals($connectors, $this->locationModel->getConnectors());
    }

    public function testEmptyStringValues(): void
    {
        $this->locationModel->setBrand('');
        $this->locationModel->setId('');
        $this->locationModel->setLocationId('');

        $this->assertEquals('', $this->locationModel->getBrand());
        $this->assertEquals('', $this->locationModel->getId());
        $this->assertEquals('', $this->locationModel->getLocationId());
    }

    public function testAddConnector(): void
    {
        // Initialize with empty array
        $this->locationModel->setConnectors([]);

        // Create a connector model
        $connector = new \App\Model\ConnectorModel();
        $connector->setType('CCS')
            ->setEvseId('evse-123')
            ->setConnectorId('connector-456')
            ->setTotal(1)
            ->setCompatible(true);

        // Add it to the location model
        $this->locationModel->addConnector($connector);

        // Check if it was added correctly
        $connectors = $this->locationModel->getConnectors();
        $this->assertCount(1, $connectors);
        $this->assertSame($connector, $connectors[0]);
    }
}
