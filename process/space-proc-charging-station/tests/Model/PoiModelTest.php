<?php

namespace App\Tests\Model;

use App\Model\PoiModel;
use PHPUnit\Framework\TestCase;

class PoiModelTest extends TestCase
{
    private PoiModel $poiModel;

    protected function setUp(): void
    {
        $this->poiModel = new PoiModel();
    }

    public function testAcceptablePaymentsGetterAndSetter(): void
    {
        $acceptablePayments = ['credit_card', 'debit_card', 'app_payment'];
        $this->poiModel->setAcceptablePayments($acceptablePayments);
        $this->assertEquals($acceptablePayments, $this->poiModel->getAcceptablePayments());
    }

    public function testAccessTypeGetterAndSetter(): void
    {
        $accessType = 'public';
        $this->poiModel->setAccessType($accessType);
        $this->assertEquals($accessType, $this->poiModel->getAccessType());
    }

    public function testNameGetterAndSetter(): void
    {
        $name = 'Charging Station Munich Central';
        $this->poiModel->setName($name);
        $this->assertEquals($name, $this->poiModel->getName());
    }

    public function testTwentyFourSevenGetterAndSetter(): void
    {
        $twentyFourSeven = true;
        $this->poiModel->setTwentyFourSeven($twentyFourSeven);
        $this->assertEquals($twentyFourSeven, $this->poiModel->getTwentyFourSeven());
    }

    public function testPartnerIDsGetterAndSetter(): void
    {
        $partnerIDs = ['partner1', 'partner2', 'partner3'];
        $this->poiModel->setPartnerIDs($partnerIDs);
        $this->assertEquals($partnerIDs, $this->poiModel->getPartnerIDs());
    }

    public function testSpecialRestrictionsGetterAndSetter(): void
    {
        $specialRestrictions = ['parking_fee', 'time_limit'];
        $this->poiModel->setSpecialRestrictions($specialRestrictions);
        $this->assertEquals($specialRestrictions, $this->poiModel->getSpecialRestrictions());
    }

    public function testNullValues(): void
    {
        $this->poiModel->setAcceptablePayments(null);
        $this->poiModel->setAccessType(null);
        $this->poiModel->setName(null);
        $this->poiModel->setPartnerIDs(null);
        $this->poiModel->setSpecialRestrictions(null);

        $this->assertNull($this->poiModel->getAcceptablePayments());
        $this->assertNull($this->poiModel->getAccessType());
        $this->assertNull($this->poiModel->getName());
        $this->assertNull($this->poiModel->getPartnerIDs());
        $this->assertNull($this->poiModel->getSpecialRestrictions());
    }

    public function testFluidInterface(): void
    {
        $result = $this->poiModel
            ->setAcceptablePayments(['credit_card'])
            ->setAccessType('public')
            ->setName('Charging Station')
            ->setTwentyFourSeven(true)
            ->setPartnerIDs(['partner1'])
            ->setSpecialRestrictions(['parking_fee']);

        $this->assertInstanceOf(PoiModel::class, $result);
    }

    public function testEmptyArrays(): void
    {
        $this->poiModel->setAcceptablePayments([]);
        $this->poiModel->setPartnerIDs([]);
        $this->poiModel->setSpecialRestrictions([]);

        $this->assertEquals([], $this->poiModel->getAcceptablePayments());
        $this->assertEquals([], $this->poiModel->getPartnerIDs());
        $this->assertEquals([], $this->poiModel->getSpecialRestrictions());
    }

    public function testEmptyStringValues(): void
    {
        $this->poiModel->setAccessType('');
        $this->poiModel->setName('');

        $this->assertEquals('', $this->poiModel->getAccessType());
        $this->assertEquals('', $this->poiModel->getName());
    }

    public function testFalseTwentyFourSeven(): void
    {
        $twentyFourSeven = false;
        $this->poiModel->setTwentyFourSeven($twentyFourSeven);
        $this->assertEquals($twentyFourSeven, $this->poiModel->getTwentyFourSeven());
    }
}
