<?php

namespace App\Tests\Model;

use App\Model\PowerLevelModel;
use App\Model\ChargeTypeAvailabilityModel;
use PHPUnit\Framework\TestCase;

class PowerLevelModelTest extends TestCase
{
    private PowerLevelModel $powerLevelModel;
    private ChargeTypeAvailabilityModel $chargeTypeAvailabilityModel;

    protected function setUp(): void
    {
        $this->powerLevelModel = new PowerLevelModel();
        $this->chargeTypeAvailabilityModel = new ChargeTypeAvailabilityModel();
    }

    public function testChargeTypeAvailabilityGetterAndSetter(): void
    {
        $this->chargeTypeAvailabilityModel
            ->setFastCharge(3)
            ->setRegularCharge(5)
            ->setSlowCharge(2)
            ->setUnknown(1);

        $this->powerLevelModel->setChargeTypeAvailability($this->chargeTypeAvailabilityModel);
        $this->assertSame($this->chargeTypeAvailabilityModel, $this->powerLevelModel->getChargeTypeAvailability());
    }

    public function testChargingCapacitiesGetterAndSetter(): void
    {
        $chargingCapacities = [
            [
                'chargingMode' => 'fast',
                'powerKw' => 150.0,
                'type' => 'DC'
            ],
            [
                'chargingMode' => 'regular',
                'powerKw' => 50.0,
                'type' => 'AC'
            ]
        ];

        $this->powerLevelModel->setChargingCapacities($chargingCapacities);
        $this->assertEquals($chargingCapacities, $this->powerLevelModel->getChargingCapacities());
    }

    public function testNullValues(): void
    {
        $this->powerLevelModel->setChargeTypeAvailability(null);
        $this->powerLevelModel->setChargingCapacities(null);

        $this->assertNull($this->powerLevelModel->getChargeTypeAvailability());
        $this->assertNull($this->powerLevelModel->getChargingCapacities());
    }

    public function testFluidInterface(): void
    {
        $result = $this->powerLevelModel
            ->setChargeTypeAvailability($this->chargeTypeAvailabilityModel)
            ->setChargingCapacities([]);

        $this->assertInstanceOf(PowerLevelModel::class, $result);
    }

    public function testEmptyChargingCapacities(): void
    {
        $chargingCapacities = [];
        $this->powerLevelModel->setChargingCapacities($chargingCapacities);
        $this->assertEquals($chargingCapacities, $this->powerLevelModel->getChargingCapacities());
    }

    public function testAddChargingCapacity(): void
    {
        // Initialize with empty array
        $this->powerLevelModel->setChargingCapacities([]);

        // Create a charging capacity model
        $chargingCapacity = new \App\Model\ChargingCapacityModel();
        $chargingCapacity->setChargingMode('fast')
            ->setPowerKw(150.0)
            ->setType('DC');

        // Add it to the power level model
        $this->powerLevelModel->addChargingCapacity($chargingCapacity);

        // Check if it was added correctly
        $chargingCapacities = $this->powerLevelModel->getChargingCapacities();
        $this->assertCount(1, $chargingCapacities);
        $this->assertSame($chargingCapacity, $chargingCapacities[0]);
    }
}
