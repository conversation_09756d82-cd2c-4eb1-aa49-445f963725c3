<?php

namespace App\Tests\Model;

use App\Model\SessionModel;
use App\Model\AddressModel;
use App\Model\CoordinateModel;
use PHPUnit\Framework\TestCase;

class SessionModelTest extends TestCase
{
    private SessionModel $sessionModel;
    private AddressModel $addressModel;
    private CoordinateModel $coordinateModel;

    protected function setUp(): void
    {
        $this->sessionModel = new SessionModel();
        $this->addressModel = new AddressModel();
        $this->coordinateModel = new CoordinateModel();
    }

    public function testIdGetterAndSetter(): void
    {
        $id = 'session-123';
        $this->sessionModel->setId($id);
        $this->assertEquals($id, $this->sessionModel->getId());
    }

    public function testFixedCommissionGetterAndSetter(): void
    {
        $fixedCommission = '5.00';
        $this->sessionModel->setFixedCommission($fixedCommission);
        $this->assertEquals($fixedCommission, $this->sessionModel->getFixedCommission());
    }

    public function testCommissionPercentageGetterAndSetter(): void
    {
        $commissionPercentage = '10.5';
        $this->sessionModel->setCommissionPercentage($commissionPercentage);
        $this->assertEquals($commissionPercentage, $this->sessionModel->getCommissionPercentage());
    }

    public function testCostGetterAndSetter(): void
    {
        $cost = '25.99';
        $this->sessionModel->setCost($cost);
        $this->assertEquals($cost, $this->sessionModel->getCost());
    }

    public function testEnergyGetterAndSetter(): void
    {
        $energy = '45.5';
        $this->sessionModel->setEnergy($energy);
        $this->assertEquals($energy, $this->sessionModel->getEnergy());
    }

    public function testStatusGetterAndSetter(): void
    {
        $status = 'completed';
        $this->sessionModel->setStatus($status);
        $this->assertEquals($status, $this->sessionModel->getStatus());
    }

    public function testStartedAtGetterAndSetter(): void
    {
        $startedAt = '2023-05-15T10:30:00Z';
        $this->sessionModel->setStartedAt($startedAt);
        $this->assertEquals($startedAt, $this->sessionModel->getStartedAt());
    }

    public function testChargingCompletedAtGetterAndSetter(): void
    {
        $chargingCompletedAt = '2023-05-15T11:45:00Z';
        $this->sessionModel->setChargingCompletedAt($chargingCompletedAt);
        $this->assertEquals($chargingCompletedAt, $this->sessionModel->getChargingCompletedAt());
    }

    public function testCreatedAtGetterAndSetter(): void
    {
        $createdAt = '2023-05-15T10:29:00Z';
        $this->sessionModel->setCreatedAt($createdAt);
        $this->assertEquals($createdAt, $this->sessionModel->getCreatedAt());
    }

    public function testUpdatedAtGetterAndSetter(): void
    {
        $updatedAt = '2023-05-15T11:46:00Z';
        $this->sessionModel->setUpdatedAt($updatedAt);
        $this->assertEquals($updatedAt, $this->sessionModel->getUpdatedAt());
    }

    public function testConnectonIdGetterAndSetter(): void
    {
        $connectonId = 'connector-456';
        $this->sessionModel->setConnectonId($connectonId);
        $this->assertEquals($connectonId, $this->sessionModel->getConnectonId());
    }

    public function testTimeZoneGetterAndSetter(): void
    {
        $timeZone = 'Europe/Berlin';
        $this->sessionModel->setTimeZone($timeZone);
        $this->assertEquals($timeZone, $this->sessionModel->getTimeZone());
    }

    public function testCurrencyGetterAndSetter(): void
    {
        $currency = 'EUR';
        $this->sessionModel->setCurrency($currency);
        $this->assertEquals($currency, $this->sessionModel->getCurrency());
    }

    public function testTotalTimeGetterAndSetter(): void
    {
        $totalTime = 75;
        $this->sessionModel->setTotalTime($totalTime);
        $this->assertEquals($totalTime, $this->sessionModel->getTotalTime());
    }

    public function testUnitTimeGetterAndSetter(): void
    {
        $unitTime = 'minutes';
        $this->sessionModel->setUnitTime($unitTime);
        $this->assertEquals($unitTime, $this->sessionModel->getUnitTime());
    }

    public function testPriceTextGetterAndSetter(): void
    {
        $priceText = '25.99 EUR';
        $this->sessionModel->setPriceText($priceText);
        $this->assertEquals($priceText, $this->sessionModel->getPriceText());
    }

    public function testPowerGetterAndSetter(): void
    {
        $power = 22.0;
        $this->sessionModel->setPower($power);
        $this->assertEquals($power, $this->sessionModel->getPower());
    }

    public function testDeltaPowerGetterAndSetter(): void
    {
        $deltaPower = 0.5;
        $this->sessionModel->setDeltaPower($deltaPower);
        $this->assertEquals($deltaPower, $this->sessionModel->getDeltaPower());
    }

    public function testNameGetterAndSetter(): void
    {
        $name = 'Charging Station 1';
        $this->sessionModel->setName($name);
        $this->assertEquals($name, $this->sessionModel->getName());
    }

    public function testLocationIdGetterAndSetter(): void
    {
        $locationId = 'location-789';
        $this->sessionModel->setLocationId($locationId);
        $this->assertEquals($locationId, $this->sessionModel->getLocationId());
    }

    public function testEvseIdGetterAndSetter(): void
    {
        $evseId = 'evse-101';
        $this->sessionModel->setEvseId($evseId);
        $this->assertEquals($evseId, $this->sessionModel->getEvseId());
    }

    public function testTypeGetterAndSetter(): void
    {
        $type = 'AC';
        $this->sessionModel->setType($type);
        $this->assertEquals($type, $this->sessionModel->getType());
    }

    public function testPowerLevelGetterAndSetter(): void
    {
        $powerLevel = 'fast';
        $this->sessionModel->setPowerLevel($powerLevel);
        $this->assertEquals($powerLevel, $this->sessionModel->getPowerLevel());
    }

    public function testAddressGetterAndSetter(): void
    {
        $this->addressModel->setCountry('Germany')
            ->setMunicipality('Munich')
            ->setStreetName('Leopoldstr.')
            ->setStreetNumber('123');

        $this->sessionModel->setAddress($this->addressModel);
        $this->assertSame($this->addressModel, $this->sessionModel->getAddress());
    }

    public function testPositionGetterAndSetter(): void
    {
        $this->coordinateModel->setLat(48.1351)
            ->setLon(11.5820);

        $this->sessionModel->setPosition($this->coordinateModel);
        $this->assertSame($this->coordinateModel, $this->sessionModel->getPosition());
    }
}
