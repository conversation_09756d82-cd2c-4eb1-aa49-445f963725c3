<?php

namespace App\Tests\Model;

use App\Model\ShowRoomEligibility;
use App\Model\ShowRoomEligibilityList;
use PHPUnit\Framework\TestCase;

class ShowRoomEligibilityListTest extends TestCase
{
    private ShowRoomEligibilityList $showRoomEligibilityList;

    protected function setUp(): void
    {
        $this->showRoomEligibilityList = new ShowRoomEligibilityList();
    }

    public function testInitialState(): void
    {
        $this->assertIsArray($this->showRoomEligibilityList->getIshowRoomEligibilities());
        $this->assertEmpty($this->showRoomEligibilityList->getIshowRoomEligibilities());
    }

    public function testSetIshowRoomEligibilities(): void
    {
        $eligibility1 = new ShowRoomEligibility();
        $eligibility1->setId('eligibility-123');
        
        $eligibility2 = new ShowRoomEligibility();
        $eligibility2->setId('eligibility-456');
        
        $eligibilities = [$eligibility1, $eligibility2];
        
        $this->showRoomEligibilityList->setIshowRoomEligibilities($eligibilities);
        
        $this->assertSame($eligibilities, $this->showRoomEligibilityList->getIshowRoomEligibilities());
        $this->assertCount(2, $this->showRoomEligibilityList->getIshowRoomEligibilities());
    }

    public function testAddShowRoomEligibility(): void
    {
        $eligibility1 = new ShowRoomEligibility();
        $eligibility1->setId('eligibility-123');
        
        $eligibility2 = new ShowRoomEligibility();
        $eligibility2->setId('eligibility-456');
        
        $this->showRoomEligibilityList->addShowRoomEligibility($eligibility1);
        $this->showRoomEligibilityList->addShowRoomEligibility($eligibility2);
        
        $result = $this->showRoomEligibilityList->getIshowRoomEligibilities();
        
        $this->assertCount(2, $result);
        $this->assertSame($eligibility1, $result[0]);
        $this->assertSame($eligibility2, $result[1]);
    }

    public function testFluidInterface(): void
    {
        $eligibility = new ShowRoomEligibility();
        $eligibility->setId('eligibility-123');
        
        $result = $this->showRoomEligibilityList->addShowRoomEligibility($eligibility);
        
        $this->assertInstanceOf(ShowRoomEligibilityList::class, $result);
    }

    public function testSetAndAddCombined(): void
    {
        $eligibility1 = new ShowRoomEligibility();
        $eligibility1->setId('eligibility-123');
        
        $eligibility2 = new ShowRoomEligibility();
        $eligibility2->setId('eligibility-456');
        
        $eligibility3 = new ShowRoomEligibility();
        $eligibility3->setId('eligibility-789');
        
        // Set initial array
        $this->showRoomEligibilityList->setIshowRoomEligibilities([$eligibility1, $eligibility2]);
        
        // Add another item
        $this->showRoomEligibilityList->addShowRoomEligibility($eligibility3);
        
        $result = $this->showRoomEligibilityList->getIshowRoomEligibilities();
        
        $this->assertCount(3, $result);
        $this->assertSame($eligibility1, $result[0]);
        $this->assertSame($eligibility2, $result[1]);
        $this->assertSame($eligibility3, $result[2]);
    }
}
