<?php

namespace App\Tests\Model;

use App\Model\ShowRoomEligibility;
use PHPUnit\Framework\TestCase;

class ShowRoomEligibilityTest extends TestCase
{
    private ShowRoomEligibility $showRoomEligibility;

    protected function setUp(): void
    {
        $this->showRoomEligibility = new ShowRoomEligibility();
    }

    public function testIdGetterAndSetter(): void
    {
        $id = 'eligibility-123';
        $this->showRoomEligibility->setId($id);
        $this->assertEquals($id, $this->showRoomEligibility->getId());
    }

    public function testEmailGetterAndSetter(): void
    {
        $email = '<EMAIL>';
        $this->showRoomEligibility->setEmail($email);
        $this->assertEquals($email, $this->showRoomEligibility->getEmail());
    }

    public function testPhoneGetterAndSetter(): void
    {
        $phone = '+49123456789';
        $this->showRoomEligibility->setPhone($phone);
        $this->assertEquals($phone, $this->showRoomEligibility->getPhone());
    }

    public function testFirstNameGetterAndSetter(): void
    {
        $firstName = 'John';
        $this->showRoomEligibility->setFirstName($firstName);
        $this->assertEquals($firstName, $this->showRoomEligibility->getFirstName());
    }

    public function testLastNameGetterAndSetter(): void
    {
        $lastName = 'Doe';
        $this->showRoomEligibility->setLastName($lastName);
        $this->assertEquals($lastName, $this->showRoomEligibility->getLastName());
    }

    public function testCountryCodeGetterAndSetter(): void
    {
        $countryCode = 'DE';
        $this->showRoomEligibility->setCountryCode($countryCode);
        $this->assertEquals($countryCode, $this->showRoomEligibility->getCountryCode());
    }

    public function testLanguageGetterAndSetter(): void
    {
        $language = 'de';
        $this->showRoomEligibility->setLanguage($language);
        $this->assertEquals($language, $this->showRoomEligibility->getLanguage());
    }

    public function testStatusGetterAndSetter(): void
    {
        $status = 'approved';
        $this->showRoomEligibility->setStatus($status);
        $this->assertEquals($status, $this->showRoomEligibility->getStatus());
    }

    public function testBenefitTypeGetterAndSetter(): void
    {
        $benefitType = 'discount';
        $this->showRoomEligibility->setBenefitType($benefitType);
        $this->assertEquals($benefitType, $this->showRoomEligibility->getBenefitType());
    }

    public function testVinGetterAndSetter(): void
    {
        $vin = 'WVWZZZ1JZXW123456';
        $this->showRoomEligibility->setVin($vin);
        $this->assertEquals($vin, $this->showRoomEligibility->getVin());
    }

    public function testFixedAmountGetterAndSetter(): void
    {
        $fixedAmount = '500.00';
        $this->showRoomEligibility->setFixedAmount($fixedAmount);
        $this->assertEquals($fixedAmount, $this->showRoomEligibility->getFixedAmount());
    }

    public function testUserIdGetterAndSetter(): void
    {
        $userId = 'user-456';
        $this->showRoomEligibility->setUserId($userId);
        $this->assertEquals($userId, $this->showRoomEligibility->getUserId());
    }

    public function testConnectionOfferIdGetterAndSetter(): void
    {
        $connectionOfferId = 'offer-789';
        $this->showRoomEligibility->setConnectionOfferId($connectionOfferId);
        $this->assertEquals($connectionOfferId, $this->showRoomEligibility->getConnectionOfferId());
    }

    public function testNullValues(): void
    {
        $this->showRoomEligibility->setId(null);
        $this->showRoomEligibility->setEmail(null);
        $this->showRoomEligibility->setPhone(null);
        
        $this->assertNull($this->showRoomEligibility->getId());
        $this->assertNull($this->showRoomEligibility->getEmail());
        $this->assertNull($this->showRoomEligibility->getPhone());
    }

    public function testFluidInterface(): void
    {
        $result = $this->showRoomEligibility
            ->setId('eligibility-123')
            ->setEmail('<EMAIL>')
            ->setFirstName('John')
            ->setLastName('Doe');
        
        $this->assertInstanceOf(ShowRoomEligibility::class, $result);
    }
}
