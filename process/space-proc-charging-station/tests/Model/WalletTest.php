<?php

namespace App\Tests\Model;

use App\Model\Wallet;
use PHPUnit\Framework\TestCase;

class WalletTest extends TestCase
{
    private Wallet $wallet;

    protected function setUp(): void
    {
        $this->wallet = new Wallet();
    }

    public function testTotalCreditsGetterAndSetter(): void
    {
        $totalCredits = '500';
        $this->wallet->setTotalCredits($totalCredits);
        $this->assertEquals($totalCredits, $this->wallet->getTotalCredits());
    }

    public function testTotalSpentInUsdGetterAndSetter(): void
    {
        $totalSpentInUsd = 250.75;
        $this->wallet->setTotalSpentInUsd($totalSpentInUsd);
        $this->assertEquals($totalSpentInUsd, $this->wallet->getTotalSpentInUsd());
    }

    public function testTotalSpentInCadGetterAndSetter(): void
    {
        $totalSpentInCad = 325.50;
        $this->wallet->setTotalSpentInCad($totalSpentInCad);
        $this->assertEquals($totalSpentInCad, $this->wallet->getTotalSpentInCad());
    }

    public function testAccountBalanceGetterAndSetter(): void
    {
        $accountBalance = '750.25';
        $this->wallet->setAccountBalance($accountBalance);
        $this->assertEquals($accountBalance, $this->wallet->getAccountBalance());
    }

    public function testWalletBalanceGetterAndSetter(): void
    {
        $walletBalance = '1000.00';
        $this->wallet->setWalletBalance($walletBalance);
        $this->assertEquals($walletBalance, $this->wallet->getWalletBalance());
    }

    public function testNullValues(): void
    {
        $this->wallet->setTotalCredits(null);
        $this->wallet->setTotalSpentInUsd(null);
        $this->wallet->setTotalSpentInCad(null);
        $this->wallet->setAccountBalance(null);
        $this->wallet->setWalletBalance(null);
        
        $this->assertNull($this->wallet->getTotalCredits());
        $this->assertNull($this->wallet->getTotalSpentInUsd());
        $this->assertNull($this->wallet->getTotalSpentInCad());
        $this->assertNull($this->wallet->getAccountBalance());
        $this->assertNull($this->wallet->getWalletBalance());
    }

    public function testFluidInterface(): void
    {
        $result = $this->wallet
            ->setTotalCredits('500')
            ->setTotalSpentInUsd(250.75)
            ->setTotalSpentInCad(325.50)
            ->setAccountBalance('750.25')
            ->setWalletBalance('1000.00');
        
        $this->assertInstanceOf(Wallet::class, $result);
    }

    public function testZeroValues(): void
    {
        $this->wallet
            ->setTotalSpentInUsd(0.0)
            ->setTotalSpentInCad(0.0);
        
        $this->assertEquals(0.0, $this->wallet->getTotalSpentInUsd());
        $this->assertEquals(0.0, $this->wallet->getTotalSpentInCad());
    }

    public function testNegativeValues(): void
    {
        $this->wallet
            ->setTotalSpentInUsd(-250.75)
            ->setTotalSpentInCad(-325.50);
        
        $this->assertEquals(-250.75, $this->wallet->getTotalSpentInUsd());
        $this->assertEquals(-325.50, $this->wallet->getTotalSpentInCad());
    }

    public function testEmptyStringValues(): void
    {
        $this->wallet
            ->setTotalCredits('')
            ->setAccountBalance('')
            ->setWalletBalance('');
        
        $this->assertEquals('', $this->wallet->getTotalCredits());
        $this->assertEquals('', $this->wallet->getAccountBalance());
        $this->assertEquals('', $this->wallet->getWalletBalance());
    }
}
