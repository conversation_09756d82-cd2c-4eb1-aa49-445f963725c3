<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\ChargingSessionService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class ChargingSessionServiceTest extends TestCase
{
    private F2mConnector $connector;
    private HttpClientInterface $client;
    private ChargingSessionService $chargingSessionService;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(F2mConnector::class);
        $this->client = $this->createMock(HttpClientInterface::class);
        $this->chargingSessionService = new ChargingSessionService($this->connector, $this->client);
    }

    public function testGetHistorySuccess(): void
    {
        $params = ['param1' => 'value1'];
        $token = 'test-token';
        $expectedData = ['session1', 'session2'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/charging-sessions/history',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                    'query' => $params,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ['data' => $expectedData]));

        $result = $this->chargingSessionService->getHistory($params, $token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetHistoryError(): void
    {
        $params = ['param1' => 'value1'];
        $token = 'test-token';
        $errorData = ['error' => 'Invalid request'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/charging-sessions/history',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                    'query' => $params,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorData));

        $result = $this->chargingSessionService->getHistory($params, $token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testStopSessionSuccess(): void
    {
        $token = 'test-token';
        $body = ['sessionId' => 'session123'];
        $expectedData = ['status' => 'stopped'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/charging-sessions/stop',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $body,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ['data' => $expectedData]));

        $result = $this->chargingSessionService->stopSession($token, $body);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testStopSessionError(): void
    {
        $token = 'test-token';
        $body = ['sessionId' => 'session123'];
        $errorData = ['error' => 'Session not found'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/charging-sessions/stop',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $body,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorData));

        $result = $this->chargingSessionService->stopSession($token, $body);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testStartSessionSuccess(): void
    {
        $token = 'test-token';
        $body = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'connectorId' => 'conn1'
        ];
        $expectedData = ['sessionId' => 'session123'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/charging-sessions/start',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $body
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ['data' => $expectedData]));

        $result = $this->chargingSessionService->startSession($token, $body);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetSessionSuccess(): void
    {
        $sessionId = 'session123';
        $token = 'test-token';
        $expectedData = ['success' => true, 'data' => ['id' => $sessionId]];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "v1/charging/session/{$sessionId}",
                [
                    'headers' => [
                        'accesstoken' => $token
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $expectedData));

        $result = $this->chargingSessionService->getSession($sessionId, $token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetCdrDataSuccess(): void
    {
        $sessionId = 'session123';
        $token = 'test-token';
        $expectedData = ['energy' => 10.5, 'duration' => 3600];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "v1/charging/session/{$sessionId}/cdr",
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ['data' => $expectedData]));

        $result = $this->chargingSessionService->getCdrData($token, $sessionId);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetPaymentMethodUrlSuccess(): void
    {
        $token = 'test-token';
        $expectedData = 'https://payment.example.com/method';

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/user/payment-method-url',
                [
                    'headers' => [
                        'accesstoken' => $token
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ['success' => $expectedData]));

        $result = $this->chargingSessionService->getPaymentMethodUrl($token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetPaymentHistorySuccess(): void
    {
        $token = 'test-token';
        $expectedData = ['transactions' => []];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/user/transactions',
                [
                    'headers' => [
                        'accept' => 'application/json',
                        'accesstoken' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $expectedData));

        $result = $this->chargingSessionService->getPaymentHistory($token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetIshowroomEligibilitySuccess(): void
    {
        $token = 'test-token';
        $email = '<EMAIL>';
        $expectedData = ['eligible' => true, 'credits' => 100];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/user/ishowroom/eligibility',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => ['email' => $email],
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ['data' => $expectedData]));

        $result = $this->chargingSessionService->getIshowroomEligibility($token, $email);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetIshowroomEligibilityError(): void
    {
        $token = 'test-token';
        $email = '<EMAIL>';
        $errorData = ['error' => 'User not eligible'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/user/ishowroom/eligibility',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => ['email' => $email],
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorData));

        $result = $this->chargingSessionService->getIshowroomEligibility($token, $email);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testGetWalletDetailSuccess(): void
    {
        $token = 'test-token';
        $expectedData = ['balance' => 500, 'currency' => 'EUR'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/user/wallet',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ['data' => $expectedData]));

        $result = $this->chargingSessionService->getWalletDetail($token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }

    public function testGetWalletDetailError(): void
    {
        $token = 'test-token';
        $errorData = ['error' => 'Wallet not found'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/user/wallet',
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json',
                    ],
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, $errorData));

        $result = $this->chargingSessionService->getWalletDetail($token);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testApplyIshowroomCreditSuccess(): void
    {
        $token = 'test-token';
        $id = 'credit123';
        $expectedData = ['success' => true, 'message' => 'Credit applied'];

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/user/ishowroom/credit',
                [
                    'headers' => [
                        'accept' => 'application/json',
                        'accesstoken' => $token,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => ['id' => $id],
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $expectedData));

        $result = $this->chargingSessionService->applyIshowroomCredit($token, $id);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($expectedData, $result->getData());
    }
}
