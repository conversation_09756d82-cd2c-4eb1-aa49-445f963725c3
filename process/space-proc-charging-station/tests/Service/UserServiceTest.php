<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Manager\ChargingStationManagementManager;
use App\Service\MongoAtlasQueryService;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class UserServiceTest extends TestCase
{
    private $mongoService;
    private $connector;
    private $userService;
    private $logger;

    protected function setUp(): void
    {
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->connector = $this->createMock(F2mConnector::class);
        $this->userService = new UserService($this->mongoService, $this->connector);

        // Set logger using reflection since it's injected via a trait
        $this->logger = $this->createMock(LoggerInterface::class);
        $reflection = new \ReflectionClass($this->userService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->userService, $this->logger);
    }

    private function createMockWSResponse(int $code, $data): WSResponse
    {
        return new WSResponse($code, $data);
    }

    public function testGetUserByUserIdFilter(): void
    {
        $userId = 'test-user-id';
        $expected = [
            [
                '$match' => ['userId' => $userId],
            ],
        ];

        $result = $this->userService->getUserByUserIdFilter($userId);

        $this->assertEquals($expected, $result);
    }

    public function testGetUserByUserIdAndVinFilter(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $expected = [
            [
                '$match' => ['userId' => $userId, 'vehicle.vin' => $vin],
            ],
        ];

        $result = $this->userService->getUserByUserIdAndVinFilter($userId, $vin);

        $this->assertEquals($expected, $result);
    }

    public function testGetUserByUserIdAndVin(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $filter = [
            [
                '$match' => ['userId' => $userId, 'vehicle.vin' => $vin],
            ],
        ];

        $expectedResponse = new WSResponse(Response::HTTP_OK, ['data' => 'test']);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting user', ['userId' => $userId, 'vin' => $vin]);

        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->with(UserService::COLLECTION, $filter)
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserByUserIdAndVin($userId, $vin);

        $this->assertSame($expectedResponse, $result);
    }

    public function testGetUserByUserId(): void
    {
        $userId = 'test-user-id';
        $filter = [
            [
                '$match' => ['userId' => $userId],
            ],
        ];

        $expectedResponse = new WSResponse(Response::HTTP_OK, ['data' => 'test']);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting user', ['userId' => $userId]);

        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->with(UserService::COLLECTION, $filter)
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserByUserId($userId);

        $this->assertSame($expectedResponse, $result);
    }

    public function testGetUserData(): void
    {
        $userId = 'test-user-id';
        $userData = [
            'documents' => [
                [
                    'userId' => $userId,
                    'name' => 'Test User',
                    'email' => '<EMAIL>'
                ]
            ]
        ];

        $expectedResponse = new WSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(UserService::COLLECTION, ['userId' => $userId])
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserData($userId);

        $this->assertEquals($userData['documents'][0], $result);
    }

    public function testGetUserDataWithEmptyResponse(): void
    {
        $userId = 'test-user-id';
        $userData = ['documents' => []];

        $expectedResponse = new WSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with(UserService::COLLECTION, ['userId' => $userId])
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserData($userId);

        $this->assertEquals([], $result);
    }

    public function testRegister(): void
    {
        $payload = ['email' => '<EMAIL>', 'password' => 'password'];
        $responseData = [
            'success' => [
                'accessToken' => 'test-token',
                'refreshToken' => 'refresh-token'
            ]
        ];

        $expectedResponse = new WSResponse(Response::HTTP_OK, $responseData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/user/enrolment',
                $payload
            )
            ->willReturn($expectedResponse);

        $result = $this->userService->register($payload);

        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testRegisterWithErrorResponse(): void
    {
        $payload = ['email' => '<EMAIL>', 'password' => 'password'];
        $responseData = ['error' => 'Invalid credentials'];

        $expectedResponse = new WSResponse(Response::HTTP_BAD_REQUEST, $responseData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'v1/user/enrolment',
                $payload
            )
            ->willReturn($expectedResponse);

        $result = $this->userService->register($payload);

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testRegisterF2mcUserInMongoDb(): void
    {
        $userId = 'test-user-id';
        $data = [
            'f2mc.accessToken' => 'test-token',
            'f2mc.refreshToken' => 'refresh-token'
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->mongoService->expects($this->once())
            ->method('updateFirstDocument')
            ->with(UserService::COLLECTION, $data, ['userId' => $userId])
            ->willReturn($expectedResponse);

        $result = $this->userService->registerF2mcUserInMongoDb($data, $userId);

        $this->assertSame($expectedResponse, $result);
    }

    public function testGetPaymentMethodUrl(): void
    {
        $token = 'test-token';
        $url = 'https://example.com/payment';

        $responseData = [
            'success' => [
                'url' => $url
            ]
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, $responseData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/user/payment-method-url',
                [
                    'headers' => [
                        'accessToken' => $token,
                    ],
                ]
            )
            ->willReturn($expectedResponse);

        $result = $this->userService->getPaymentMethodUrl($token);

        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($url, $result->getData());
    }

    public function testGetPaymentMethodUrlWithError(): void
    {
        $token = 'test-token';
        $errorData = ['error' => 'Invalid token'];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_UNAUTHORIZED, $errorData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($expectedResponse);

        $result = $this->userService->getPaymentMethodUrl($token);

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testUpdateF2mcUserInMongoDb(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';
        $data = ['status' => 'active'];

        $updateData = [
            "vehicle.$.chargingStation" => $data
        ];

        $this->mongoService->expects($this->once())
            ->method('updateFirstDocument')
            ->with(
                UserService::COLLECTION,
                $updateData,
                ['userId' => $userId, 'vehicle.vin' => $vin]
            );

        $this->userService->updateF2mcUserInMongoDb($data, $userId, $vin);
    }

    public function testGetAccountLinkUrl(): void
    {
        $token = 'test-token';
        $providerShortCode = 'provider-code';
        $url = 'https://example.com/link';

        $responseData = [
            'data' => [
                'url' => $url
            ]
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_CREATED, $responseData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                'v1/user/account-link-url/' . $providerShortCode,
                [
                    'headers' => [
                        'accessToken' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn($expectedResponse);

        $result = $this->userService->getAccountLinkUrl($token, $providerShortCode);

        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals($responseData['data'], $result->getData());
    }

    public function testGetAccountLinkUrlWithMissingData(): void
    {
        $token = 'test-token';
        $providerShortCode = 'provider-code';
        $responseData = ['success' => true]; // Missing 'data' field

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_CREATED, $responseData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($expectedResponse);

        $result = $this->userService->getAccountLinkUrl($token, $providerShortCode);

        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testGetAccountLinkUrlWithErrorResponse(): void
    {
        $token = 'test-token';
        $providerShortCode = 'provider-code';
        $responseData = ['error' => 'Invalid token'];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_UNAUTHORIZED, $responseData);

        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($expectedResponse);

        $result = $this->userService->getAccountLinkUrl($token, $providerShortCode);

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testUpdateDBChargingStation(): void
    {
        $filter = ['userId' => 'test-user-id'];
        $data = ['status' => 'active'];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(UserService::COLLECTION, $filter, $data, true)
            ->willReturn($expectedResponse);

        $result = $this->userService->updateDBChargingStation($filter, $data);

        $this->assertSame($expectedResponse, $result);
    }

    public function testGetUserByProfileIdFilter(): void
    {
        $profileId = 'test-profile-id';
        $expected = [
            [
                '$match' => ['f2mc.userId' => $profileId],
            ],
        ];

        $result = $this->userService->getUserByProfileIdFilter($profileId);

        $this->assertEquals($expected, $result);
    }

    public function testGetUserByProfileId(): void
    {
        $profileId = 'test-profile-id';
        $filter = [
            [
                '$match' => ['f2mc.userId' => $profileId],
            ],
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['data' => 'test']);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting user', ['f2mc.userId' => $profileId]);

        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->with(UserService::COLLECTION, $filter)
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserByProfileId($profileId);

        $this->assertSame($expectedResponse, $result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatus(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';

        $userData = [
            'document' => [
                'userId' => $userId,
                'vehicle' => [
                    [
                        'type' => 'BEV',
                        'featureCodes' => [
                            [
                                'code' => ChargingStationManagementManager::CSM_FEATURE_CODE,
                                'config' => [
                                    'enrolmentStatus' => 'noAccountLinking'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->with('userData', ['userId' => $userId])
            ->willReturn($findOneResponse);

        $expectedUpdateData = [
            'vehicle' => [
                [
                    'type' => 'BEV',
                    'featureCodes' => [
                        [
                            'code' => ChargingStationManagementManager::CSM_FEATURE_CODE,
                            'config' => [
                                'enrolmentStatus' => $status
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $updateResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->mongoService->expects($this->once())
            ->method('updateFirstDocument')
            ->with('userData', $expectedUpdateData, ['userId' => $userId])
            ->willReturn($updateResponse);

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatus($userId, $status);

        $this->assertTrue($result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusWithError(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_NOT_FOUND, 'User not found');

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->with('userData', ['userId' => $userId])
            ->willReturn($findOneResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('No user data found'));

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatus($userId, $status);

        $this->assertFalse($result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusWithEmptyDocument(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';

        $userData = [
            'document' => []
        ];

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->with('userData', ['userId' => $userId])
            ->willReturn($findOneResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Empty user document'));

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatus($userId, $status);

        $this->assertFalse($result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusWithException(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->willThrowException(new \Exception('Database error'));

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Exception while updating feature code status'));

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatus($userId, $status);

        $this->assertFalse($result);
    }

    public function testUpdateUserVehicleId(): void
    {
        $userId = 'test-user-id';
        $vehicleId = 'vehicle-123';
        $vin = 'test-vin';

        $userData = [
            'documents' => [
                [
                    'userId' => $userId,
                    'vehicle' => [
                        [
                            'vin' => $vin,
                            'chargingStation' => []
                        ]
                    ]
                ]
            ]
        ];

        $findResponse = $this->createMockWSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('find')
            ->with('userData', ['userId' => $userId])
            ->willReturn($findResponse);

        $expectedVehicleData = [
            [
                'vin' => $vin,
                'chargingStation' => [
                    'vehicleId' => $vehicleId
                ]
            ]
        ];

        $updateResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->mongoService->expects($this->once())
            ->method('updateFirstDocument')
            ->with('userData', ['vehicle' => $expectedVehicleData], ['userId' => $userId])
            ->willReturn($updateResponse);

        $result = $this->userService->updateUserVehicleId($userId, $vehicleId, $vin);

        $this->assertSame($updateResponse, $result);
    }

    public function testFindUserVehicleByVin(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';

        $userData = [
            'document' => [
                'vehicle' => [
                    [
                        'vin' => $vin,
                        'model' => 'Test Model'
                    ]
                ],
                'f2mc' => [
                    'accessToken' => 'test-token'
                ]
            ]
        ];

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->willReturn($findOneResponse);

        $result = $this->userService->findUserVehicleByVin($userId, $vin);

        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $resultData = $result->getData();
        $this->assertTrue($resultData['success']);
        $this->assertEquals($userData['document']['vehicle'][0], $resultData['vehicle']);
        $this->assertEquals($userData['document']['f2mc'], $resultData['f2mc']);
    }

    public function testFindUserVehicleByVinWithError(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_NOT_FOUND, 'User not found');

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->willReturn($findOneResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error while fetching user vehicle by vin'));

        $result = $this->userService->findUserVehicleByVin($userId, $vin);

        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
        $resultData = $result->getData();
        $this->assertArrayHasKey('error', $resultData);
        $this->assertEquals('Error while fetching user vehicle data', $resultData['error']['message']);
    }

    public function testFindUserVehicleByVinWithException(): void
    {
        $userId = 'test-user-id';
        $vin = 'test-vin';

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->willThrowException(new \Exception('Database error'));

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Error while fetching user vehicle by vin'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->userService->findUserVehicleByVin($userId, $vin);
    }

    public function testGetUserByF2MUserIdFilter(): void
    {
        $f2mUserId = 'test-f2m-user-id';
        $chargingSessionId = 'test-session-id';
        $expected = [
            [
                '$match' => [
                    'f2mc.userId' => $f2mUserId,
                    'vehicle.chargingStation.session.id' => $chargingSessionId
                ]
            ]
        ];

        $reflection = new \ReflectionClass($this->userService);
        $method = $reflection->getMethod('getUserByF2MUserIdFilter');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->userService, [$f2mUserId, $chargingSessionId]);

        $this->assertEquals($expected, $result);
    }

    public function testGetUserByF2MUserId(): void
    {
        $f2mUserId = 'test-f2m-user-id';
        $chargingSessionId = 'test-session-id';
        $filter = [
            [
                '$match' => [
                    'f2mc.userId' => $f2mUserId,
                    'vehicle.chargingStation.session.id' => $chargingSessionId
                ]
            ]
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['data' => 'test']);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting user', ['userId' => $f2mUserId, 'chargingSessionId' => $chargingSessionId]);

        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->with(UserService::COLLECTION, $filter)
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserByF2MUserId($f2mUserId, $chargingSessionId);

        $this->assertSame($expectedResponse, $result);
    }

    public function testUpdateUserRemoteCommandStatus(): void
    {
        $f2mUserId = 'test-f2m-user-id';
        $chargingSessionId = 'test-session-id';
        $status = 'completed';

        $filter = [
            'f2mc.userId' => $f2mUserId,
            'vehicle.chargingStation.session.id' => $chargingSessionId
        ];

        $fields = [
            'vehicle.$.chargingStation.session.remoteCommandStatus' => $status
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Updating user remote command status', [
                'f2mUserId' => $f2mUserId,
                'chargingSessionId' => $chargingSessionId,
                'status' => $status
            ]);

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(UserService::COLLECTION, $filter, $fields)
            ->willReturn($expectedResponse);

        $result = $this->userService->updateUserRemoteCommandStatus($f2mUserId, $chargingSessionId, $status);

        $this->assertSame($expectedResponse, $result);
    }

    public function testGetUserByOtherIdsFilter(): void
    {
        $f2mUserId = 'test-f2m-user-id';
        $locationId = 'test-location-id';
        $evseId = 'test-evse-id';
        $connectorId = 'test-connector-id';

        $expected = [
            [
                '$match' => [
                    'f2mc.userId' => $f2mUserId,
                    'vehicle.chargingStation.session.id' => null,
                    'vehicle.chargingStation.session.locationId' => $locationId,
                    'vehicle.chargingStation.session.evseId' => $evseId,
                    'vehicle.chargingStation.session.connectorId' => $connectorId
                ]
            ]
        ];

        $reflection = new \ReflectionClass($this->userService);
        $method = $reflection->getMethod('getUserByOtherIdsFilter');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->userService, [$f2mUserId, $locationId, $evseId, $connectorId]);

        $this->assertEquals($expected, $result);
    }

    public function testGetUserByOtherIds(): void
    {
        $f2mUserId = 'test-f2m-user-id';
        $locationId = 'test-location-id';
        $evseId = 'test-evse-id';
        $connectorId = 'test-connector-id';

        $filter = [
            [
                '$match' => [
                    'f2mc.userId' => $f2mUserId,
                    'vehicle.chargingStation.session.id' => null,
                    'vehicle.chargingStation.session.locationId' => $locationId,
                    'vehicle.chargingStation.session.evseId' => $evseId,
                    'vehicle.chargingStation.session.connectorId' => $connectorId
                ]
            ]
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['data' => 'test']);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Getting user', [
                'f2mUserId' => $f2mUserId,
                'locationId' => $locationId,
                'evseId' => $evseId,
                'connectorId' => $connectorId
            ]);

        $this->mongoService->expects($this->once())
            ->method('aggregate')
            ->with(UserService::COLLECTION, $filter)
            ->willReturn($expectedResponse);

        $result = $this->userService->getUserByOtherIds($f2mUserId, $locationId, $evseId, $connectorId);

        $this->assertSame($expectedResponse, $result);
    }

    public function testUpdateUserRemoteCommandStatusUsingOtherIds(): void
    {
        $f2mUserId = 'test-f2m-user-id';
        $locationId = 'test-location-id';
        $evseId = 'test-evse-id';
        $connectorId = 'test-connector-id';
        $chargingSessionId = 'test-session-id';
        $status = 'completed';

        $filter = [
            'f2mc.userId' => $f2mUserId,
            'vehicle.chargingStation.session.id' => null,
            'vehicle.chargingStation.session.locationId' => $locationId,
            'vehicle.chargingStation.session.evseId' => $evseId,
            'vehicle.chargingStation.session.connectorId' => $connectorId
        ];

        $fields = [
            'vehicle.$.chargingStation.session.id' => $chargingSessionId,
            'vehicle.$.chargingStation.session.remoteCommandStatus' => $status
        ];

        $expectedResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Updating user remote command status', [
                'f2mUserId' => $f2mUserId,
                'locationId' => $locationId,
                'evseId' => $evseId,
                'connectorId' => $connectorId,
                'chargingSessionId' => $chargingSessionId,
                'status' => $status
            ]);

        $this->mongoService->expects($this->once())
            ->method('updateOne')
            ->with(UserService::COLLECTION, $filter, $fields)
            ->willReturn($expectedResponse);

        $result = $this->userService->updateUserRemoteCommandStatusUsingOtherIds(
            $f2mUserId,
            $locationId,
            $evseId,
            $connectorId,
            $chargingSessionId,
            $status
        );

        $this->assertSame($expectedResponse, $result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusAndVin(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';
        $vin = 'test-vin';

        $userData = [
            'document' => [
                'userId' => $userId,
                'vehicle' => [
                    [
                        'vin' => $vin,
                        'type' => 'BEV',
                        'featureCodes' => [
                            [
                                'code' => ChargingStationManagementManager::CSM_FEATURE_CODE,
                                'config' => [
                                    'enrolmentStatus' => 'noAccountLinking'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->with('userData', [
                'userId' => $userId,
                'vehicle.vin' => $vin
            ])
            ->willReturn($findOneResponse);

        $updatedFeatureCodes = [
            [
                'code' => ChargingStationManagementManager::CSM_FEATURE_CODE,
                'config' => [
                    'enrolmentStatus' => $status
                ]
            ]
        ];

        $updateResponse = $this->createMockWSResponse(Response::HTTP_OK, ['success' => true]);

        $this->mongoService->expects($this->once())
            ->method('updateFirstDocument')
            ->with('userData', [
                'vehicle.0.featureCodes' => $updatedFeatureCodes
            ], ['userId' => $userId])
            ->willReturn($updateResponse);

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatusAndVin($userId, $status, $vin);

        $this->assertTrue($result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusAndVinWithError(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';
        $vin = 'test-vin';

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_NOT_FOUND, 'User not found');

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->with('userData', [
                'userId' => $userId,
                'vehicle.vin' => $vin
            ])
            ->willReturn($findOneResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('No user data found'));

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatusAndVin($userId, $status, $vin);

        $this->assertFalse($result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusAndVinWithEmptyDocument(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';
        $vin = 'test-vin';

        $userData = [
            'document' => []
        ];

        $findOneResponse = $this->createMockWSResponse(Response::HTTP_OK, json_encode($userData));

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->with('userData', [
                'userId' => $userId,
                'vehicle.vin' => $vin
            ])
            ->willReturn($findOneResponse);

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Empty user document'));

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatusAndVin($userId, $status, $vin);

        $this->assertFalse($result);
    }

    public function testUpdateUserVehiclesFeatureCodeByStatusAndVinWithException(): void
    {
        $userId = 'test-user-id';
        $status = 'complete';
        $vin = 'test-vin';

        $this->mongoService->expects($this->once())
            ->method('findOne')
            ->willThrowException(new \Exception('Database error'));

        $this->logger->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Exception while updating feature code status'));

        $result = $this->userService->updateUserVehiclesFeatureCodeByStatusAndVin($userId, $status, $vin);

        $this->assertFalse($result);
    }
}
