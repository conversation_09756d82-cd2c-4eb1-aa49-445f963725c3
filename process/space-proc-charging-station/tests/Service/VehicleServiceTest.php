<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\VehicleService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class VehicleServiceTest extends TestCase
{
    private $connector;
    private $httpClient;
    private $vehicleService;

    protected function setUp(): void
    {
        $this->connector = $this->createMock(F2mConnector::class);
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->vehicleService = new VehicleService($this->connector, $this->httpClient);
    }

    /**
     * Helper method to create a mock WSResponse
     */
    private function createMockWSResponse(int $code, $data): WSResponse
    {
        return new WSResponse($code, $data);
    }

    public function testGetList(): void
    {
        // Test data
        $accessToken = 'test-access-token';
        $responseData = [
            'success' => [
                'vehicles' => [
                    [
                        'id' => 1,
                        'vin' => 'VIN123456789',
                        'make' => 'Test Make',
                        'model' => 'Test Model'
                    ]
                ]
            ]
        ];

        // Expected request parameters
        $expectedUri = 'v1/user/vehicles';
        $expectedOptions = [
            'headers' => [
                'accesstoken' => $accessToken,
                'accept' => 'application/json'
            ],
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(200, $responseData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                $expectedUri,
                $expectedOptions
            )
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->getList($accessToken);

        // Assert the result
        $this->assertEquals(200, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testAdd(): void
    {
        // Test data
        $accessToken = 'test-access-token';
        $payload = [
            'vin' => 'VIN123456789',
            'make' => 'Test Make',
            'model' => 'Test Model'
        ];
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];
        $responseData = [
            'success' => [
                'id' => 1,
                'vin' => 'VIN123456789'
            ]
        ];

        // Expected request parameters
        $expectedUri = 'v1/user/vehicles';
        $expectedOptions = [
            'headers' => [
                'accept' => 'application/json',
                'accesstoken' => $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(201, $responseData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                $expectedUri,
                $expectedOptions
            )
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->add($data);

        // Assert the result
        $this->assertEquals(201, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testUpdate(): void
    {
        // Test data
        $vehicleId = 123;
        $accessToken = 'test-access-token';
        $payload = [
            'make' => 'Updated Make',
            'model' => 'Updated Model'
        ];
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];
        $responseData = [
            'success' => [
                'id' => $vehicleId,
                'make' => 'Updated Make',
                'model' => 'Updated Model'
            ]
        ];

        // Expected request parameters
        $expectedUri = "v1/user/vehicles/{$vehicleId}/default";
        $expectedOptions = [
            'headers' => [
                'accept' => 'application/json',
                'accesstoken' => $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(200, $responseData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_PUT,
                $expectedUri,
                $expectedOptions
            )
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->update($data);

        // Assert the result
        $this->assertEquals(200, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testDelete(): void
    {
        // Test data
        $vehicleId = 123;
        $accessToken = 'test-access-token';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];
        $responseData = [
            'success' => true
        ];

        // Expected request parameters
        $expectedUri = "v1/user/vehicles/{$vehicleId}";
        $expectedOptions = [
            'headers' => [
                'accept' => 'application/json',
                'accesstoken' => $accessToken,
            ]
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(200, $responseData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_DELETE,
                $expectedUri,
                $expectedOptions
            )
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->delete($data);

        // Assert the result
        $this->assertEquals(200, $result->getCode());
        $this->assertEquals($responseData, $result->getData());
    }

    public function testGetListWithError(): void
    {
        // Test data
        $accessToken = 'invalid-token';
        $errorData = [
            'error' => [
                'message' => 'Invalid token'
            ]
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(401, $errorData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->getList($accessToken);

        // Assert the result
        $this->assertEquals(401, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testAddWithError(): void
    {
        // Test data
        $accessToken = 'test-access-token';
        $payload = [
            'vin' => 'INVALID_VIN',
            'make' => 'Test Make',
            'model' => 'Test Model'
        ];
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];
        $errorData = [
            'error' => [
                'message' => 'Invalid VIN format'
            ]
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(400, $errorData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->add($data);

        // Assert the result
        $this->assertEquals(400, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testUpdateWithError(): void
    {
        // Test data
        $vehicleId = 999; // Non-existent vehicle ID
        $accessToken = 'test-access-token';
        $payload = [
            'make' => 'Updated Make',
            'model' => 'Updated Model'
        ];
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];
        $errorData = [
            'error' => [
                'message' => 'Vehicle not found'
            ]
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(404, $errorData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->update($data);

        // Assert the result
        $this->assertEquals(404, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }

    public function testDeleteWithError(): void
    {
        // Test data
        $vehicleId = 999; // Non-existent vehicle ID
        $accessToken = 'test-access-token';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];
        $errorData = [
            'error' => [
                'message' => 'Vehicle not found'
            ]
        ];

        // Mock the connector response
        $connectorResponse = $this->createMockWSResponse(404, $errorData);
        $this->connector->expects($this->once())
            ->method('callF2mc')
            ->willReturn($connectorResponse);

        // Call the method
        $result = $this->vehicleService->delete($data);

        // Assert the result
        $this->assertEquals(404, $result->getCode());
        $this->assertEquals($errorData, $result->getData());
    }
}
