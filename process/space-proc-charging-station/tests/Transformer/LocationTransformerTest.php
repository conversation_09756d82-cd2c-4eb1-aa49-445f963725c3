<?php

namespace App\Tests\Transformer;

use App\Model\AddressModel;
use App\Model\AvailabilityModel;
use App\Model\ChargeTypeAvailabilityModel;
use App\Model\ChargingCapacityModel;
use App\Model\ConnectorModel;
use App\Model\CoordinateModel;
use App\Model\LocationModel;
use App\Model\PoiModel;
use App\Model\PowerLevelModel;
use App\Transformer\LocationTransformer;
use PHPUnit\Framework\TestCase;

class LocationTransformerTest extends TestCase
{
    public function testMapLocationDataWithFullData(): void
    {
        // Arrange
        $data = [
            [
                'id' => 'location-123',
                'locationId' => 'loc-456',
                'operator' => 'ChargePoint',
                'twentyFourSeven' => true,
                'addressDetail' => [
                    'country' => 'Germany',
                    'countryCodeISO3' => 'DEU',
                    'countrySecondarySubdivision' => 'Munich',
                    'countrySubdivision' => 'Bavaria',
                    'countrySubdivisionName' => 'Bayern',
                    'countryTertiarySubdivision' => 'Schwabing',
                    'extendedPostalCode' => '80331-1234',
                    'address' => 'Leopoldstr.',
                    'city' => 'Munich',
                    'state' => 'Bavaria',
                    'postalCode' => '80331',
                    'streetNumber' => '123',
                    'localName' => 'München'
                ],
                'coordinates' => [
                    'latitude' => 48.1351,
                    'longitude' => 11.5820
                ],
                'evses' => [
                    [
                        'evseId' => 'evse-123',
                        'connectors' => [
                            [
                                'connectorId' => 'connector-456',
                                'type' => 'CCS',
                                'status' => 'AVAILABLE',
                                'speed' => 'FAST',
                                'power' => 150.0,
                                'priceText' => '0.49 EUR/kWh',
                                'speedShortCode' => 'fast',
                                'capabilities' => ['remote_start', 'remote_stop']
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Act
        $result = LocationTransformer::mapLocationData($data);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertInstanceOf(LocationModel::class, $result[0]);

        // Check location properties
        $location = $result[0];
        $this->assertEquals('location-123', $location->getId());
        $this->assertEquals('loc-456', $location->getLocationId());
        $this->assertEquals('ChargePoint', $location->getBrand());

        // Check address
        $this->assertInstanceOf(AddressModel::class, $location->getAddress());
        $address = $location->getAddress();
        $this->assertEquals('Germany', $address->getCountry());
        $this->assertEquals('Germany', $address->getCountryCode());
        $this->assertEquals('DEU', $address->getCountryCodeISO3());
        $this->assertEquals('Munich', $address->getCountrySecondarySubdivision());
        $this->assertEquals('Bavaria', $address->getCountrySubdivision());
        $this->assertEquals('Bayern', $address->getCountrySubdivisionName());
        $this->assertEquals('Schwabing', $address->getCountryTertiarySubdivision());
        $this->assertEquals('80331-1234', $address->getExtendedPostalCode());
        $this->assertEquals('Leopoldstr., Munich, Bavaria, 80331, Germany', $address->getFreeformAddress());
        $this->assertEquals('München', $address->getLocalName());
        $this->assertEquals('Munich', $address->getMunicipality());
        $this->assertEquals('80331', $address->getPostalCode());
        $this->assertEquals('Leopoldstr.', $address->getStreetName());
        $this->assertEquals('123', $address->getStreetNumber());

        // Check POI
        $this->assertInstanceOf(PoiModel::class, $location->getPoi());
        $poi = $location->getPoi();
        $this->assertEquals('ChargePoint', $poi->getName());
        $this->assertTrue($poi->getTwentyFourSeven());
        $this->assertIsArray($poi->getAcceptablePayments());
        $this->assertCount(4, $poi->getAcceptablePayments());

        // Check coordinates
        $this->assertInstanceOf(CoordinateModel::class, $location->getPosition());
        $position = $location->getPosition();
        $this->assertEquals(48.1351, $position->getLat());
        $this->assertEquals(11.5820, $position->getLon());

        // Check connectors
        $connectors = $location->getConnectors();
        $this->assertIsArray($connectors);
        $this->assertCount(1, $connectors);
        $this->assertInstanceOf(ConnectorModel::class, $connectors[0]);

        $connector = $connectors[0];
        $this->assertEquals('CCS', $connector->getType());
        $this->assertEquals('evse-123', $connector->getEvseId());
        $this->assertEquals('connector-456', $connector->getConnectorId());
        $this->assertEquals('0.49 EUR/kWh', $connector->getPriceText());
        $this->assertEquals('fast', $connector->getSpeedShortCode());
        $this->assertEquals(['remote_start', 'remote_stop'], $connector->getCapabilities());
        $this->assertEquals(1, $connector->getTotal());
        $this->assertFalse($connector->getCompatible());

        // Check availability
        $this->assertInstanceOf(AvailabilityModel::class, $connector->getAvailability());
        $availability = $connector->getAvailability();
        $this->assertEquals(1, $availability->getAvailable());
        $this->assertEquals(0, $availability->getOccupied());
        $this->assertEquals(0, $availability->getOutOfService());
        $this->assertEquals(0, $availability->getReserved());
        $this->assertEquals(0, $availability->getUnknown());

        // Check power level
        $this->assertInstanceOf(PowerLevelModel::class, $connector->getPowerLevel());
        $powerLevel = $connector->getPowerLevel();

        // Check charge type availability
        $this->assertInstanceOf(ChargeTypeAvailabilityModel::class, $powerLevel->getChargeTypeAvailability());
        $chargeTypeAvailability = $powerLevel->getChargeTypeAvailability();
        $this->assertEquals(1, $chargeTypeAvailability->getFastCharge());
        $this->assertEquals(0, $chargeTypeAvailability->getRegularCharge());
        $this->assertEquals(0, $chargeTypeAvailability->getSlowCharge());
        $this->assertEquals(0, $chargeTypeAvailability->getUnknown());

        // Check charging capacities
        $this->assertIsArray($powerLevel->getChargingCapacities());
        $this->assertCount(1, $powerLevel->getChargingCapacities());
        $this->assertInstanceOf(ChargingCapacityModel::class, $powerLevel->getChargingCapacities()[0]);

        $chargingCapacity = $powerLevel->getChargingCapacities()[0];
        $this->assertEquals('', $chargingCapacity->getChargingMode());
        $this->assertEquals(150.0, $chargingCapacity->getPowerKw());
        $this->assertEquals('', $chargingCapacity->getType());
    }

    public function testMapLocationDataWithMinimalData(): void
    {
        // Arrange
        $data = [
            [
                'id' => 'location-123',
                'locationId' => 'loc-456',
                'operator' => 'ChargePoint'
            ]
        ];

        // Act
        $result = LocationTransformer::mapLocationData($data);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertInstanceOf(LocationModel::class, $result[0]);

        $location = $result[0];
        $this->assertEquals('location-123', $location->getId());
        $this->assertEquals('loc-456', $location->getLocationId());
        $this->assertEquals('ChargePoint', $location->getBrand());

        // Check POI
        $this->assertInstanceOf(PoiModel::class, $location->getPoi());
        $poi = $location->getPoi();
        $this->assertEquals('ChargePoint', $poi->getName());
        $this->assertFalse($poi->getTwentyFourSeven());

        // Initialize connectors
        $location->setConnectors([]);

        // Check connectors
        $connectors = $location->getConnectors();
        $this->assertIsArray($connectors);
        $this->assertEmpty($connectors);
    }

    public function testMapLocationDataWithEmptyArray(): void
    {
        // Arrange
        $data = [];

        // Act
        $result = LocationTransformer::mapLocationData($data);

        // Assert
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function testMapLocationDataWithOfflineConnector(): void
    {
        // Arrange
        $data = [
            [
                'id' => 'location-123',
                'locationId' => 'loc-456',
                'operator' => 'ChargePoint',
                'evses' => [
                    [
                        'evseId' => 'evse-123',
                        'connectors' => [
                            [
                                'connectorId' => 'connector-456',
                                'type' => 'CCS',
                                'status' => 'OFFLINE',
                                'speed' => 'REGULAR',
                                'power' => 50.0
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Act
        $result = LocationTransformer::mapLocationData($data);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);

        $location = $result[0];
        $connectors = $location->getConnectors();
        $this->assertCount(1, $connectors);

        $connector = $connectors[0];
        $availability = $connector->getAvailability();
        $this->assertEquals(0, $availability->getAvailable());
        $this->assertEquals(1, $availability->getOutOfService());

        $powerLevel = $connector->getPowerLevel();
        $chargeTypeAvailability = $powerLevel->getChargeTypeAvailability();
        $this->assertEquals(0, $chargeTypeAvailability->getFastCharge());
        $this->assertEquals(1, $chargeTypeAvailability->getRegularCharge());
    }
}
