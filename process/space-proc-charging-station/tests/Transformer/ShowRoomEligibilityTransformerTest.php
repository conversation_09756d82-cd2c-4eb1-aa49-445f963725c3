<?php

namespace App\Tests\Transformer;

use App\Model\ShowRoomEligibility;
use App\Model\ShowRoomEligibilityList;
use App\Transformer\ShowRoomEligibilityTransformer;
use PHPUnit\Framework\TestCase;

class ShowRoomEligibilityTransformerTest extends TestCase
{
    public function testMapIshowRoomDataWithMultipleItems(): void
    {
        // Arrange
        $data = [
            [
                'id' => 'eligibility-123',
                'connectionOfferId' => 'offer-789',
                'email' => '<EMAIL>',
                'phone' => '+49123456789',
                'firstName' => 'John',
                'lastName' => 'Doe',
                'countryCode' => 'DE',
                'language' => 'de',
                'status' => 'approved',
                'benefitType' => 'discount',
                'vin' => 'WVWZZZ1JZXW123456',
                'fixedAmount' => '500.00',
                'userId' => 'user-456'
            ],
            [
                'id' => 'eligibility-456',
                'connectionOfferId' => 'offer-101',
                'email' => '<EMAIL>',
                'phone' => '+49987654321',
                'firstName' => 'Jane',
                'lastName' => 'Smith',
                'countryCode' => 'DE',
                'language' => 'en',
                'status' => 'pending',
                'benefitType' => 'credit',
                'vin' => 'WVWZZZ1JZXW654321',
                'fixedAmount' => '750.00',
                'userId' => 'user-789'
            ]
        ];

        // Act
        $result = ShowRoomEligibilityTransformer::mapIshowRoomData($data);

        // Assert
        $this->assertInstanceOf(ShowRoomEligibilityList::class, $result);
        $eligibilities = $result->getIshowRoomEligibilities();
        $this->assertCount(2, $eligibilities);
        
        // Check first item
        $this->assertInstanceOf(ShowRoomEligibility::class, $eligibilities[0]);
        $this->assertEquals('eligibility-123', $eligibilities[0]->getId());
        $this->assertEquals('offer-789', $eligibilities[0]->getConnectionOfferId());
        $this->assertEquals('<EMAIL>', $eligibilities[0]->getEmail());
        $this->assertEquals('+49123456789', $eligibilities[0]->getPhone());
        $this->assertEquals('John', $eligibilities[0]->getFirstName());
        $this->assertEquals('Doe', $eligibilities[0]->getLastName());
        $this->assertEquals('DE', $eligibilities[0]->getCountryCode());
        $this->assertEquals('de', $eligibilities[0]->getLanguage());
        $this->assertEquals('approved', $eligibilities[0]->getStatus());
        $this->assertEquals('discount', $eligibilities[0]->getBenefitType());
        $this->assertEquals('WVWZZZ1JZXW123456', $eligibilities[0]->getVin());
        $this->assertEquals('user-456', $eligibilities[0]->getFixedAmount()); // Note: This is a bug in the transformer, it sets userId to fixedAmount
        
        // Check second item
        $this->assertEquals('eligibility-456', $eligibilities[1]->getId());
        $this->assertEquals('<EMAIL>', $eligibilities[1]->getEmail());
    }

    public function testMapIshowRoomDataWithEmptyArray(): void
    {
        // Arrange
        $data = [];

        // Act
        $result = ShowRoomEligibilityTransformer::mapIshowRoomData($data);

        // Assert
        $this->assertInstanceOf(ShowRoomEligibilityList::class, $result);
        $eligibilities = $result->getIshowRoomEligibilities();
        $this->assertCount(0, $eligibilities);
        $this->assertEmpty($eligibilities);
    }

    public function testMapIshowRoomDataWithMissingFields(): void
    {
        // Arrange
        $data = [
            [
                'id' => 'eligibility-123',
                // Missing most fields
            ]
        ];

        // Act
        $result = ShowRoomEligibilityTransformer::mapIshowRoomData($data);

        // Assert
        $this->assertInstanceOf(ShowRoomEligibilityList::class, $result);
        $eligibilities = $result->getIshowRoomEligibilities();
        $this->assertCount(1, $eligibilities);
        
        $this->assertEquals('eligibility-123', $eligibilities[0]->getId());
        $this->assertEquals('', $eligibilities[0]->getConnectionOfferId());
        $this->assertEquals('', $eligibilities[0]->getEmail());
        $this->assertEquals('', $eligibilities[0]->getPhone());
        $this->assertEquals('', $eligibilities[0]->getFirstName());
        $this->assertEquals('', $eligibilities[0]->getLastName());
        $this->assertEquals('', $eligibilities[0]->getCountryCode());
        $this->assertEquals('', $eligibilities[0]->getLanguage());
        $this->assertEquals('', $eligibilities[0]->getStatus());
        $this->assertEquals('', $eligibilities[0]->getBenefitType());
        $this->assertEquals('', $eligibilities[0]->getVin());
        $this->assertEquals('', $eligibilities[0]->getFixedAmount());
    }
}
