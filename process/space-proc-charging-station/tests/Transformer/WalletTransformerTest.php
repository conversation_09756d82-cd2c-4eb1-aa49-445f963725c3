<?php

namespace App\Tests\Transformer;

use App\Model\Wallet;
use App\Transformer\WalletTransformer;
use PHPUnit\Framework\TestCase;

class WalletTransformerTest extends TestCase
{
    public function testMapWalletDataWithAllFields(): void
    {
        // Arrange
        $data = [
            'totalSpentInUsd' => 250.75,
            'totalSpentInCad' => 325.50,
            'accountBalance' => '750.25',
            'totalCredits' => '500',
            'walletBalance' => '1000.00'
        ];

        // Act
        $wallet = WalletTransformer::mapWalletData($data);

        // Assert
        $this->assertInstanceOf(Wallet::class, $wallet);
        $this->assertEquals(250.75, $wallet->getTotalSpentInUsd());
        $this->assertEquals(325.50, $wallet->getTotalSpentInCad());
        $this->assertEquals('750.25', $wallet->getAccountBalance());
        $this->assertEquals('500', $wallet->getTotalCredits());
        $this->assertEquals('1000.00', $wallet->getWalletBalance());
    }

    public function testMapWalletDataWithMissingFields(): void
    {
        // Arrange
        $data = [
            'totalSpentInUsd' => 250.75,
            'totalSpentInCad' => 325.50,
            'accountBalance' => '750.25'
        ];

        // Act
        $wallet = WalletTransformer::mapWalletData($data);

        // Assert
        $this->assertInstanceOf(Wallet::class, $wallet);
        $this->assertEquals(250.75, $wallet->getTotalSpentInUsd());
        $this->assertEquals(325.50, $wallet->getTotalSpentInCad());
        $this->assertEquals('750.25', $wallet->getAccountBalance());
    }

    public function testMapWalletDataWithEmptyData(): void
    {
        // Arrange
        $data = [];

        // Act
        $wallet = WalletTransformer::mapWalletData($data);

        // Assert
        $this->assertInstanceOf(Wallet::class, $wallet);
        $this->assertEquals(0, $wallet->getTotalSpentInUsd());
        $this->assertEquals(0, $wallet->getTotalSpentInCad());
        $this->assertEquals('', $wallet->getAccountBalance());
    }

    public function testMapWalletDataWithNullValues(): void
    {
        // Arrange
        $data = [
            'totalSpentInUsd' => null,
            'totalSpentInCad' => null,
            'accountBalance' => null,
            'totalCredits' => null,
            'walletBalance' => null
        ];

        // Act
        $wallet = WalletTransformer::mapWalletData($data);

        // Assert
        $this->assertInstanceOf(Wallet::class, $wallet);
        $this->assertEquals(0, $wallet->getTotalSpentInUsd());
        $this->assertEquals(0, $wallet->getTotalSpentInCad());
        $this->assertEquals('', $wallet->getAccountBalance());
    }
}
