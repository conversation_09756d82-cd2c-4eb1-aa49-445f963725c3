doctrine_mongodb:
  connections:
    default:
      server: >
        mongodb://%env(MONGODB_USER)%:%env(MONGODB_PASSWORD)%@%env(MONGODB_HOSTS)%/?replicaSet=%env(MONGODB_REPLICA)%&ssl=true&authSource=admin&retryWrites=true&w=majority&appName=Space&%env(MONGODB_POOL_PARAMS)%
      options: {}
  default_database: "%env(MONGODB_DB)%"
  document_managers:
    default:
      auto_mapping: true
      mappings:
        App:
          is_bundle: false
          type: attribute
          dir: "%kernel.project_dir%/src/Document"
          prefix: App\Document
          alias: App
