<?php

namespace App\Controller;

use App\Manager\IntrospectionManager;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class IntrospectionController extends AbstractController
{
    use ValidationResponseTrait;

    #[OA\Parameter(
        name: 'token',
        in: 'header',
        description: 'CVS token',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country Code',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        required: false,
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'SPACEWEB']
        )
    )]
    #[OA\Parameter(
        name: 'os',
        in: 'query',
        schema: new OA\Schema(
            type: 'string',
            enum: ['ios', 'and']
        )
    )]
    #[OA\Tag(name: 'Introspection API')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'userId'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[Route('/v1/introspection', name: 'app_introspection', methods: [Request::METHOD_GET])]
    public function index(Request $request, IntrospectionManager $introspectionManager, ValidatorInterface $validator): Response
    {
        $userCvsToken = $request->headers->get('token');
        $brand = $request->query->get('brand');
        $source = mb_strtoupper($request->query->get('source', 'SPACEWEB'));
        $country = ($source == "SPACEWEB") ? '' : strtolower($request->get('country'));
        $device = ($source == "SPACEWEB") ? '' : strtolower($request->get('os'));

        $constraints = [
            'userCvsToken' => new Assert\NotBlank(),
            'brand' => new Assert\NotBlank(),
        ];

        $dataToValidate = [
            'userCvsToken' => $userCvsToken,
            'brand' => $brand,
        ];

        if ($source === "APP") {
            $constraints['device'] = new Assert\NotBlank();
            $dataToValidate['device'] = $device;
        }

        $errors = $validator->validate(
            $dataToValidate,
            new Assert\Collection($constraints)
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }

        $response = $introspectionManager->getCLPGigyaId($userCvsToken, $brand, $source, $device, $country)->toArray();

        return $this->json($response['content'], $response['code']);
    }



    #[OA\Parameter(
        name: 'cvsCode',
        in: 'header',
        description: 'CVS token',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country Code',
        schema: new OA\Schema(type: 'string')
    )]

    #[OA\Parameter(
        name: 'redirect_uri',
        in: 'query',
        required: true,
        description: 'redirect uri',
        schema: new OA\Schema(type: 'string')
    )]

    #[OA\Parameter(
        name: 'source',
        in: 'query',
        required: false,
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'SPACEWEB']
        )
    )]

    #[OA\Tag(name: 'Authorize API')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'access_token'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[Route('/v1/authorize', name: 'app_authorize', methods: [Request::METHOD_GET])]
    public function authorize(Request $request, IntrospectionManager $introspectionManager, ValidatorInterface $validator): Response
    {
        $cvsCode = $request->headers->get('cvsCode');
        $brand = $request->query->get('brand');
        $source = mb_strtoupper($request->query->get('source', 'SPACEWEB'));
        $country =  strtolower($request->get('country'));
        $redirectUri =  strtolower($request->get('redirect_uri'));

        $constraints = [
            'cvsCode' => new Assert\NotBlank(),
            'brand' => new Assert\NotBlank(),
            'redirectUri' =>  new Assert\NotBlank(),
        ];

        $dataToValidate = [
            'cvsCode' => $cvsCode,
            'brand' => $brand,
            'redirectUri' => $redirectUri,
        ];


        $errors = $validator->validate(
            $dataToValidate,
            new Assert\Collection($constraints)
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $introspectionManager->getAccessToken($cvsCode, $redirectUri, $brand, $country, $source)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Parameter(
        name: 'code',
        in: 'header',
        required: true,
        description: 'code',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        required: true,
        description: 'Brand',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        required: true,
        description: 'Country Code',
        schema: new OA\Schema(type: 'string')
    )]

    #[OA\Parameter(
        name: 'redirect_uri',
        in: 'query',
        required: true,
        description: 'redirect uri',
        schema: new OA\Schema(type: 'string')
    )]

    #[OA\Parameter(
        name: 'source',
        in: 'query',
        required: true,
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'SPACEWEB']
        )
    )]
    #[OA\Parameter(
        name: 'idp',
        in: 'query',
        required: true,
        schema: new OA\Schema(
            type: 'string',
            enum: ['CVS', 'GIGYA']
        )
    )]

    #[OA\Tag(name: 'Authorize API')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'access_token'),
                    new OA\Property(property: 'token_type'),
                    new OA\Property(property: 'expires_in'),
                    new OA\Property(property: 'id_token'),
                    new OA\Property(property: 'refresh_token'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[Route('/v1/token', name: 'app_token', methods: [Request::METHOD_GET])]
    public function getToken(Request $request, IntrospectionManager $introspectionManager, ValidatorInterface $validator): Response
    {
        $code = $request->headers->get('code');
        $brand = $request->query->get('brand');
        $source = mb_strtoupper($request->query->get('source', 'SPACEWEB'));
        $country =  strtolower($request->get('country'));
        $redirectUri =  strtolower($request->get('redirect_uri'));
        $idp =  strtoupper($request->get('idp', "CVS"));

        $constraints = [
            'code' => new Assert\NotBlank(),
            'brand' => new Assert\NotBlank(),
            'redirectUri' =>  new Assert\NotBlank(),
            'idp' => new Assert\Choice(['CVS', 'GIGYA'])
        ];

        $dataToValidate = [
            'code' => $code,
            'brand' => $brand,
            'redirectUri' => $redirectUri,
            'idp' => $idp
        ];


        $errors = $validator->validate(
            $dataToValidate,
            new Assert\Collection($constraints)
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $introspectionManager->getToken($code, $redirectUri, $brand, $country, $source, $idp)->toArray();

        return $this->json($response['content'], $response['code']);
    }
}
