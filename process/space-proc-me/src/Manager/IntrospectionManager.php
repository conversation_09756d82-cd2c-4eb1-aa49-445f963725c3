<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\IntrospectionService;
use App\Service\ProfileService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Introspection Manager
 */
class IntrospectionManager
{
    use LoggerTrait;

    private ProfileService $profileService;

    private IntrospectionService $service;

    private SerializerInterface $serializer;

    private UserManager $userManager;

    public function __construct(IntrospectionService $introspectionService, SerializerInterface $serializer, ProfileService $profileService, UserManager $userManager)
    {
        $this->service = $introspectionService;
        $this->serializer = $serializer;
        $this->profileService = $profileService;
        $this->userManager = $userManager;
    }

    /**
     * Get Gigya ID by C<PERSON> token
     */
    public function getGigyaId(string $userCvsToken, string $brand, string $source, string $device, string $country): ResponseArrayFormat
    {
        try {
            // get userCvsId
            $userCvsResponse = $this->service->getUserCvsId($userCvsToken, $brand, $source, $device, $country);
            if (Response::HTTP_OK != $userCvsResponse->getCode()) {
                return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
            }
            $userCvsId = $userCvsResponse->getData();

            // read data from mongoDB by user CVS ID
            $response = $this->service->readUserData($userCvsId);
            if (Response::HTTP_OK == $response->getCode()) {
                $userData = json_decode($response->getData(), true);
                $userData = $userData['documents'][0] ?? null;
                if (isset($userData['userId'])) {
                    return new SuccessResponse(['userId' => $userData['userId']]);
                }
            }

            $response = $this->service->getGigyaData($userCvsId, $brand);
            if (Response::HTTP_OK == $response->getCode() && !empty($response->getData()['success'])) {
                $userData = $response->getData()['success'];
                $userId = $userData['userId'] ?? '';
                if ($userId && $this->service->isUserExist($userId)) {
                    $response = $this->service->addCvsToUser($userData, $userCvsId, $brand);
                    if (Response::HTTP_OK == $response->getCode()) {
                        return new SuccessResponse(['userId' => $userId]);
                    }

                    return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
                }
                $response = $this->service->createUser($userData, $userCvsId, $brand);
                if (Response::HTTP_OK == $response->getCode()) {
                    return new SuccessResponse(['userId' => $userId]);
                }
            }
            $this->logger->info("unauthorized GigyaId =>  IntrospectionManager::getGigyaId,  userCvsToken : $userCvsToken, brand : $brand");

            return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
        } catch (\Exception $e) {
            $this->logger->error('Catched Exception IntrospectionManager::getGigyaId ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    public function getCLPGigyaId(string $userCvsToken, string $brand, string $source, string $device, string $country): ResponseArrayFormat
    {
        try {
            // get userCvsId
            $userCvsResponse = $this->service->getUserCLPCvsId($userCvsToken, $brand, $source, $device, $country);
            if (Response::HTTP_OK != $userCvsResponse->getCode()) {
                return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
            }
            $userCvsId = $userCvsResponse->getData();

            if (str_starts_with($userCvsId, 'ST-'))
            {
                $response = $this->service->readUserDataByUserDbId($userCvsId);
                if (Response::HTTP_OK == $response->getCode()) {
                    $userData = json_decode($response->getData(), true);
                    $userData = $userData['documents'][0] ?? null;
                    if (isset($userData['userId'])) {
                        return new SuccessResponse(['userId' => $userData['userId']]);
                    }
                }
                $response = $this->profileService->getUserData($userCvsId);
                if (Response::HTTP_OK == $response->getCode() && !empty($response->getData()['success'])) {
                    $userData = $response->getData()['success'];
                    $userId = $userData['idp_Gigya_id'] ?? '';
                    $response = $this->userManager->createUser($userData);
                    if (Response::HTTP_CREATED == $response->getCode()) {
                        return new SuccessResponse(['userId' => $userId]);
                    }
                }
            }
            else
            {
                $response = $this->service->readUserData($userCvsId);
                if (Response::HTTP_OK == $response->getCode()) {
                    $userData = json_decode($response->getData(), true);
                    $userData = $userData['documents'][0] ?? null;
                    if (isset($userData['userId'])) {
                        return new SuccessResponse(['userId' => $userData['userId']]);
                    }
                }

                $response = $this->service->getGigyaData($userCvsId, $brand);
                if (Response::HTTP_OK == $response->getCode() && !empty($response->getData()['success'])) {
                    $userData = $response->getData()['success'];
                    $userId = $userData['userId'] ?? '';
                    if ($userId && $this->service->isUserExist($userId)) {
                        $response = $this->service->addCvsToUser($userData, $userCvsId, $brand);
                        if (Response::HTTP_OK == $response->getCode()) {
                            return new SuccessResponse(['userId' => $userId]);
                        }

                        return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
                    }
                    $response = $this->service->createUser($userData, $userCvsId, $brand);
                    if (Response::HTTP_CREATED == $response->getCode()) {
                        return new SuccessResponse(['userId' => $userId]);
                    }
                }
            }  
            $this->logger->info("unauthorized GigyaId =>  IntrospectionManager::getGigyaId,  userCvsToken : $userCvsToken, brand : $brand");

            return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
        } catch (\Exception $e) {
            $this->logger->error('Catched Exception IntrospectionManager::getGigyaId ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    public function getAccessToken($cvsCode, $redirectUri, $brand, $country, $source){

        try {
            // get access token 
            $accessTokenResponse = $this->service->getAccessToken($cvsCode, $redirectUri,$brand, $country, $source);
            if (Response::HTTP_OK != $accessTokenResponse->getCode()) {
                return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
            }
            
            if (Response::HTTP_OK == $accessTokenResponse->getCode()) {
                $accessToken = $accessTokenResponse->getData();
                return new SuccessResponse(['access_token' => $accessToken]);
            }

            $this->logger->info("unauthorized  =>  IntrospectionManager::getAccessToken,  cvsCode : $cvsCode, brand : $brand");

            return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
        } catch (\Exception $e) {
            $this->logger->error('Catched Exception IntrospectionManager::getAccessToken ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }

    }

    public function getToken(string $code, string $redirectUri, string $brand, string $country, string $source, string $idp) {
        try {
            // get access token 
            $tokenResponse = $this->service->getToken($code, $redirectUri,$brand, $country, $source, $idp);
            if (Response::HTTP_OK != $tokenResponse->getCode()) {
                return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
            }
            
            if (Response::HTTP_OK == $tokenResponse->getCode()) {
                return new SuccessResponse($tokenResponse->getData());
            }

            $this->logger->info("unauthorized  =>  IntrospectionManager::getAccessToken,  code : $code, brand : $brand");

            return new ErrorResponse('unauthorized', Response::HTTP_UNAUTHORIZED);
        } catch (\Exception $e) {
            $this->logger->error('Catched Exception IntrospectionManager::getAccessToken ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
