<?php

namespace App\Service;

use App\Connector\SystemIdpConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Introspection service
 */
class IntrospectionService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer,
        private SystemIdpConnector $systemIdpConnector
    ) {
    }

    /**
     * Get user CVS ID from sys-idp microservice
     */
    public function getUserCvsId(string $userCvsToken, string $brand, string $source, string $device, string $country): WSResponse
    {
        try {
            $this->logger->info("IntrospectionService::getUserCvsId for userCvsToken : $userCvsToken , brand = $brand, device = $device , source = $source, country = $country");
            $response = $this->systemIdpConnector->call('GET', "/v1/introspection?brand=$brand&source=$source&os=$device&country=$country", ['headers' => ['cvsToken' => $userCvsToken]]);
            $userCvsId = $response->getData()['success']['cvsId'] ?? '';
            if ($userCvsId) {
                return new WSResponse(Response::HTTP_OK, $userCvsId);
            }

            return new WSResponse(Response::HTTP_BAD_REQUEST, '');
        } catch (Exception $e) {
            $this->logger->error('VehicleService::getVehicles : Cached Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Get user CVS ID from sys-idp microservice
     */
    public function getUserCLPCvsId(string $userCvsToken, string $brand, string $source, string $device, string $country): WSResponse
    {
        try {
            $this->logger->info("IntrospectionService::getUserCLPCvsId for userCvsToken : $userCvsToken , brand = $brand, device = $device , source = $source, country = $country");
            $response = $this->systemIdpConnector->call('GET', "/v1/cvs/introspect?brand=$brand&source=$source&os=$device&country=$country", ['headers' => ['cvsToken' => $userCvsToken]]);
            $userCvsId = $response->getData()['success']['cvsId'] ?? '';
            if ($userCvsId) {
                return new WSResponse(Response::HTTP_OK, $userCvsId);
            }

            return new WSResponse(Response::HTTP_BAD_REQUEST, '');
        } catch (Exception $e) {
            $this->logger->error('VehicleService::getVehicles : Cached Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }


    /**
     * Get user data from Mongo DB by user CVS ID
     */
    public function readUserData(string $userCvsId): WSResponse
    {
        return $this->mongoService->find(self::COLLECTION, ['userPsaId.cvsId' => $userCvsId]);
    }

    /**
     * Get user data from Mongo DB by user CVS ID
     */
    public function readUserDataByUserDbId(string $userCvsId): WSResponse
    {
        return $this->mongoService->find(self::COLLECTION, ['userDbId' => $userCvsId]);
    }

    public function getGigyaData(string $userCvsId, string $brand): WSResponse
    {
        try {
            $this->logger->info('IntrospectionService::getGigyaData for userCvsId : ' . $userCvsId);

            return $this->systemIdpConnector->call('GET', "/v1/users?brand=$brand", ['headers' => ['userCvsId' => $userCvsId]]);
        } catch (Exception $e) {
            $this->logger->error('VehicleService::getVehicles : Cached Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function isUserExist(string $userId): bool
    {
        $this->logger->info('IntrospectionService::getGigyaDataFromDB for userId : ' . $userId);
        $userData = $this->mongoService->find(self::COLLECTION, ['userId' => $userId]);
        $userData = json_decode($userData->getData(), true);
        if (empty($userData['documents'])) {
            return false;
        }

        return true;
    }

    /**
     * create new user data
     */
    public function createUser(array $userData, string $userCvsId, string $brand): WSResponse
    {
        $userPsaId = $userData['userPsaId'];
        if ($userPsaId[$brand] == $userCvsId) {
            $userPsaIdData = json_decode($this->serializer->serialize(['brand' => $brand, 'cvsId' => $userCvsId], 'json'), true);
            $userData['userPsaId'] = [$userPsaIdData];
            return $this->mongoService->insertOne(self::COLLECTION, $userData);
        } else {
            throw new Exception('unauthorized', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * insert new CVS to user
     */
    public function addCvsToUser(array $userData, string $userCvsId, string $brand): WSResponse
    {
        $filter = ['userId' => $userData['userId']];
        $userPsaId = $userData['userPsaId'];
        if ($userPsaId[$brand] == $userCvsId) {
            $userPsaIdData = json_decode($this->serializer->serialize(['brand' => $brand, 'cvsId' => $userCvsId], 'json'), true);
            $fields = ['userPsaId' => $userPsaIdData];
            return $this->mongoService->updatePush(self::COLLECTION, $filter, $fields);
        } else {
            throw new Exception('unauthorized', Response::HTTP_BAD_REQUEST);
        }
    }


    /**
     * Get Access token from sys-idp microservice
     */
    public function getAccessToken(string $cvsCode, string $redirect_uri, string $brand, string $country, string $source): WSResponse
    {
        try {
            $this->logger->info("IntrospectionService::getAccessToken for cvsCode : $cvsCode , brand = $brand, source = $source, country = $country");
            $response = $this->systemIdpConnector->call('GET', "/v1/authorize?brand=$brand&source=$source&country=$country&redirect_uri=$redirect_uri", ['headers' => ['cvsCode' => $cvsCode]]);
            $cvsToken = $response->getData()['success']['access_token'] ?? '';
            if ($cvsToken) {
                return new WSResponse(Response::HTTP_OK, $cvsToken);
            }

            return new WSResponse(Response::HTTP_BAD_REQUEST, '');
        } catch (Exception $e) {
            $this->logger->error('IntrospectionService::getAccessToken : Cached Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function getToken(string $code, string $redirectUri, string $brand, string $country, string $source, string $idp)
    {
        try {
            $endpoint = "";
            $options = [];
            if ($idp == "CVS") {
                $endpoint = "/v1/cvs/token";
                $options = [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'cvsCode' => $code
                    ],
                    'query' => [
                        'brand' => $brand,
                        'country' => $country,
                        'source' => $source,
                        'redirect_uri' => $redirectUri
                    ]
                ];
            } elseif ($idp == "GIGYA") {
                $endpoint = "/v1/gigya/token";
                $options = [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'code' => $code
                    ],
                    'query' => [
                        'redirect_uri' => $redirectUri
                    ]
                ];
            }

            $this->logger->info("IntrospectionService::getToken");
            $wsResponse = $this->systemIdpConnector->call(
                Request::METHOD_GET,
                $endpoint,
                $options
            );

            if (Response::HTTP_OK == $wsResponse->getCode()) {
                $response = $wsResponse->getData()['success'];
                if (isset($response['access_token'])) {
                    return new WSResponse($wsResponse->getCode(), $response);
                }
                return new WSResponse(Response::HTTP_UNAUTHORIZED, $response);
            }

            return new WSResponse(Response::HTTP_BAD_REQUEST, '');
        } catch (Exception $e) {
            $this->logger->error('IntrospectionService::getAccessToken : Cached Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
