<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

/**
 * Kind of HttpClientInterface decorator.
 */
class CustomHttpClient
{
    use LoggerTrait;

    private InternalHttpClientService $client;

    public function __construct(InternalHttpClientService $client)
    {
        $this->client = $client;
    }

    public function request(string $method, string $url, array $options = []): WSResponse
    {
        try {
            $this->logger->info("Call URL: {$url} {$method}");
            $response = $this->client->request($method, $url, $options);
            $this->logger->debug('Full response logs: {logs}', ['logs' => $response->getInfo('debug')]);
            $statusCode = $response->getStatusCode();
            $data = Response::HTTP_NO_CONTENT != $statusCode ? $response->toArray(false) : [];
            $wsResponse = new WSResponse($statusCode, $data);

            return $wsResponse;
        } catch (\Exception $e) {
            $this->logger->error('Cached Exception : CustomHttpClient::request '.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
