<?php

namespace App\Manager;

use Exception;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Webmozart\Assert\Assert;

use App\Helper\VehicleTypeEntities;
use App\Service\CorvetService;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\BrandHelper;
use App\Model\CatalogModel;
use App\DataMapper\CatalogDataMapper;
use App\Model\PriceModel;
use App\Model\OfferModel;
use App\Service\CatalogService;
use App\Service\ContribService;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use App\Manager\UserManager;
use App\Service\ImageCheckerService;
use function React\Promise\all;

/**
 * Catalog Manager.
 */
class CatalogManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    const SOURCE_APP = 'APP';
    const WS_CONNECTED_SERVICES_SECTION = "CONNECTED_SERVICES";
    const WS_AFTERSALES_SERVICES = "AFTERSALES_SERVICES";
    const NAVCO = "NAVCO";
    const ZAR = "ZAR";
    const NAVCOZAR = "NAVCOZAR";
    const TMTS = "TMTS";
    const DIMBO = "DIMBO";
    const LEV = "LEV";
    const LEV_BEV = 'LEV_BEV';
    const LEV_PHEV = 'LEV_PHEV';
    const PRIVILEGE = 'DSCP';
    const RACCESS = 'RACCESS';
    const CONNECTEDALARM = 'CONNECTEDALARM';
    const EV_ROUTING_APP = 'EV_ROUTING_APP';
    const DIGITALKEY = 'DIGITALKEY';
    const AE_CALL = 'AE_CALL';
    const PARTNERSERVICE = 'PARTNERSERVICE';
    const TRIPS_IN_THE_CLOUD = 'TripsintheCloud';
    const STOLEN_VEHICLE = 'SECURITY';
    const BUNDLE = 'BUNDLE';
    const EMERGENCY = 'EMERGENCY';

    const APP_GROUP_NAMES = [
        self::NAVCO    => 'navco',
        self::ZAR      => 'zar',
        self::NAVCOZAR => 'navcozar',
        self::DIMBO    => 'dimbo',
        self::TMTS     => 'tmts',
        self::LEV      => 'remotelev',
        self::LEV_BEV  => 'remotelev_bev',
        self::LEV_PHEV => 'remotelev_phev',
        self::PRIVILEGE => 'dscp',
        self::RACCESS => 'raccess',
        self::CONNECTEDALARM => 'connectedalarm',
        self::EV_ROUTING_APP => 'ev_routing_app',
        self::DIGITALKEY => 'digitalkey',
        self::AE_CALL => 'ae_call',
        self::PARTNERSERVICE => 'partnerservice',
        self::TRIPS_IN_THE_CLOUD => 'tripsinthecloud',
        self::STOLEN_VEHICLE => 'SECURITY',
        self::EMERGENCY=> 'EMERGENCY',
        self::BUNDLE => 'bundle'
    ];
    private $item;

    public function __construct(private CatalogService $service, private SerializerInterface $serializer, private ContribService $contribService, private ImageCheckerService $imageCheckerService, private ValidatorInterface $validator, private CorvetService $corvetService) {}

    private function getCorvetData(
        string $vin,
        string $brand
    ): ?array {
        return $this->corvetService->getData($vin, $brand);
    }

    public function getCategory(?string $familyName): string
    {
        $connectedServices = ['NAVCOZAR', 'TMTS', 'NAVCO', 'ZAR', 'LEV', 'PHEV', 'BEV', 'RACCESS', 'CONNECTEDALARM', 'DIGITALKEY', 'AE_CALL', 'EV_ROUTING_APP', 'PARTNERSERVICE', 'TRIPS_IN_THE_CLOUD', 'STOLEN_VEHICLE'];
        $afterSalesServices = ['DIMBO', 'PRIVILEGE'];
        if (in_array($familyName, $connectedServices)) {
            return 'CONNECTED_SERVICES';
        } elseif (in_array($familyName, $afterSalesServices)) {
            return 'AFTERSALES_SERVICES';
        } else {
            return 'OTHERS';
        }
    }

    protected static function extractCatalogueGroupName(array $item): ?string
    {

        if ($item['groupName'] === self::NAVCO && $item['groupMemberType'] === "Base Products")
            return self::NAVCO;

        if ($item['groupName'] === self::NAVCO && $item['groupMemberType'] === "Add On Services" && $item['familyName'] === self::ZAR)
            return self::ZAR;

        if ($item['groupName'] === self::NAVCOZAR && $item['familyName'] === self::NAVCOZAR)
            return self::NAVCOZAR;

        if ($item['groupName'] === self::TMTS)
            return self::TMTS;

        if ($item['groupName'] === self::RACCESS)
            return self::RACCESS;    

        if ($item['groupName'] === self::LEV && ($item['familyName'] === "PHEV" || $item['familyName'] === "BEV"))
            return self::LEV;

        if ($item['groupName'] === self::DIMBO && $item['familyName'] === self::DIMBO)
            return self::DIMBO;

        if ($item['groupName'] === self::RACCESS)
            return self::RACCESS;        
        if ($item['groupName'] === self::PRIVILEGE && $item['familyName'] === self::PRIVILEGE)
            return self::PRIVILEGE;
            
        if ($item['groupName'] === self::CONNECTEDALARM)
            return self::CONNECTEDALARM;     
            
        if ($item['groupName'] === self::EV_ROUTING_APP)
            return self::EV_ROUTING_APP;           
            
        if($item['groupName'] === self::DIGITALKEY)
            return self::DIGITALKEY;

        if ($item['groupName'] === self::AE_CALL)
        return self::AE_CALL; 

        if ($item['groupName'] === self::PARTNERSERVICE)
            return self::PARTNERSERVICE;  
        
        if ($item['groupName'] === self::TRIPS_IN_THE_CLOUD)
            return self::TRIPS_IN_THE_CLOUD; 

        if ($item['groupName'] === self::STOLEN_VEHICLE)
            return self::STOLEN_VEHICLE;

        if ($item['groupName'] === self::EMERGENCY)
            return self::EMERGENCY;         

        if (strtoupper($item['type']) === self::BUNDLE && !empty($item['standaloneProducts']))
            return self::BUNDLE;


        return null;
    }

    private function getManagedAttributes(array $attributes): ?array
    {
        $managedAttributes  = [];
        foreach ($attributes as $attribute) {
            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DO9':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D32':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DYR':
                    if (substr($attribute, 3, 2) == "17") {
                        $managedAttributes[] = trim($attribute);
                    }
                    break;
            }
        }
        return $managedAttributes;
    }


    private function _checkVehicleHorE($brand, $country, $vin, $source)
    {
        $response = $this->getCorvetData($vin, $brand);
        if (!isset($response['error'])) {
            $allAttributes = $response['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            return in_array($vehicleTypeNumber  , ['02', '03', '04', '05', '06']);
        }
        return false;
    }

    protected function getContribData($brand, $culture, $data, $country, $source, $vin): array
    {
        $params = [
            'brand'   => $brand,
            'country' => $country,
            'source'  => $source,
        ];
        
        $isElectricOrHybrid = $this->_checkVehicleHorE($params['brand'], $params['country'], $vin, $params['source']);
        $responses = [];
        $validProducts = [];
        $productIds = [];

        foreach ($data as $product) {
            $productId = $product['id'] ?? '';
            $groupName = static::extractCatalogueGroupName($product);
            if (($groupName != self::BUNDLE) || ($groupName === self::LEV && !$isElectricOrHybrid)) {
                continue;
            }
            $key = $groupName == self::BUNDLE ? $product['groupName'] : $groupName;
            $validProducts[$key] = $product;
            if ($productId) {
                $productIds[] = $productId;
            }
        }

        $allContribs = [];
        if (!empty($productIds)) {
            $allContribs = $this->contribService->getContribInfoByProductIds(implode(',', $productIds), $brand, $culture, self::SOURCE_APP);
        }

        foreach ($validProducts as $key => $product) {
            if (!is_array($allContribs[$product['id'] ?? ''] ?? '')) {
                throw new Exception('Contrib data is not an array', Response::HTTP_BAD_REQUEST);
            }
            $responses[$key] = [
                'product' => $product,
                'contrib' => $allContribs[$product['id'] ?? ''] ?? []
            ];
        }

        $contribs = [];
        foreach ($responses as $groupName => $response) {
            try {
                $product = $response['product'];
                $content = $response['contrib'];
                
                $contrib = (new CatalogDataMapper)->setItem($product)->transform($content);
                $category = $this->getCategory($groupName);
                if (in_array($params['source'],['APP','SPACEWEB'])) {
                    $category = strtolower($category);
                    $contrib['id'] = strtolower($product['type']) == strtolower(self::BUNDLE) ? strtolower($product['groupName']) :self::APP_GROUP_NAMES[$groupName] ?? '' ;
                    if ($contrib['id'] === 'remotelev') {
                        $contrib['id'] = ($product['familyName'] === "BEV") ? self::APP_GROUP_NAMES[self::LEV_BEV] : self::APP_GROUP_NAMES[self::LEV_PHEV];
                    }
                } else {
                    $contrib['type'] = $groupName;
                }
                $contrib['category'] = strtolower($product['type']) == strtolower(self::BUNDLE) ?  strtolower($this->getCategory(self::BUNDLE)) : $category;
                $contribs[] = $contrib;
            } catch (Exception $e) {
                $this->logger->error("Exception: ServiceConnecteService::getContribData ==>" . $e->getMessage());
                throw new Exception($e->getMessage(), $e->getCode());
            }
        }
        return $contribs;
    }

    /**
     * get Catalog data.
     */
    public function getCatalog(array $params): SuccessResponse|ErrorResponse
    {
        $this->logger->info('=> Call Catalog API : ' . __METHOD__ . ' with parameters : ', $params);
        try {

            $catalogResponse = $this->service->getCatalog($params);
            if (Response::HTTP_OK == $catalogResponse->getCode()) {
                Assert::isArray($catalogResponse->getData());
                Assert::keyExists($catalogResponse->getData(), 'success');
                $catalogResponseData = $catalogResponse->getData()['success'];
                $brand = $params['brand'] ?? '';
                $cultureCode = $params['language'] . '-' . $params['country'];
                $vin = $params['vin'] ?? '';
                $country = $params['country'] ??'';
                $source = $params['source'] ?? '';
                $contribData = $this->getContribData($brand, $cultureCode, $catalogResponseData, $country, $source, $vin);

                return new SuccessResponse($contribData);
            }
            $responseData = $catalogResponse->getData();
            Assert::isArray($responseData);
            Assert::keyExists($responseData, 'error');
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;

            $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $result);

            return new ErrorResponse($result, $catalogResponse->getCode());
        } catch (Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . 'Catched Exception CatalogManager::getCatalog ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
