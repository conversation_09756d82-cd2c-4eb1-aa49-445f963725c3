<?php

namespace App\Manager;

use App\Dto\AddCustomerGarageInputDTO;
use App\Dto\AddVehicleInputDTO;
use App\Dto\AddVehicleOutputDTO;
use App\Dto\EditVehicleInputDTO;
use App\Event\SpaceVehicleUpdatedEvent;
use App\Helper\BrandHelper;
use App\Helper\BrandProvider;
use App\Helper\CultureHelper;
use App\Helper\ErrorResponse;
use App\Helper\RefreshVehicleHelper;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\VehicleTypeEntities;
use App\Helper\WSResponse;
use App\Manager\Visual3DManager;
use App\Model\SystemVehicleData;
use App\Model\VehicleListV2\VehicleListResponseMapper;
use App\Model\VehicleModel;
use App\MongoDB\UserData\UserDataDocument\UserDataDocument;
use App\MongoDB\UserData\UserDataDocument\Vehicle;
use App\MongoDB\UserData\UserDataDocument\VehicleMapper;
use App\Service\CorvetService;
use App\Service\FeatureCodeService;
use App\Service\MongoAtlasQueryService;
use App\Service\RefreshVehicleInterface;
use App\Service\SystemSdprClient;
use App\Service\SystemUserDataClient;
use App\Service\UserDataService;
use App\Service\VehicleLabelService;
use App\Service\VehicleService;
use App\Service\XFVehicleRefreshService;
use App\Service\XPVehicleRefreshService;
use App\Manager\SystemUserDBManager;
use App\Trait\LoggerTrait;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Throwable;
use Webmozart\Assert\Assert;
use App\Manager\CatalogManager;
use App\Mapper\EditVehicleMapper;
use App\Service\SystemUserDBService;
use stdClass;

/**
 * User vehicle Manager.
 */
class VehicleManager
{
    use LoggerTrait;

    const ELIGIBILITY_NAVCOZAR = 'navcozar';
    const ELIGIBILITY_REMOTELEV = 'remotelev';
    const ELIGIBILITY_NAC  = 'nac';

    public function __construct(
        private VehicleService $service,
        private SerializerInterface $serializer,
        private DenormalizerInterface $denormalizer,
        private ValidatorInterface $validator,
        private MongoAtlasQueryService $mongoService,
        private EventDispatcherInterface $dispatcher,
        private XPVehicleRefreshService $xPVehicleRefreshService,
        private XFVehicleRefreshService $xFVehicleRefreshService,
        private UserDataService $userDataService,
        private CorvetService $corvetService,
        private SystemUserDataClient $systemUserDataClient,
        private Visual3DManager $visual3DManager,
        private BrandHelper $brandHelper,
        private SystemSdprClient $systemSdprClient,
        private FeatureCodeService $featureCodeService,
        private VehicleLabelService $vehicleLabelService,
        private NormalizerInterface $normalizer,
        private CatalogManager $catalogManager,
        private SubscriptionManager $subscriptionManager,
        private SystemUserDBService $systemUserDBService,
        private SystemUserDBManager $systemUserDBManager
    ) {
    }

    /**
     * Get Vehicles On Order.
     */
    public function getVehicles(string $userId): ResponseArrayFormat
    {
        try {
            $response = $this->service->getVehiclesOnOrder($userId);
            if (Response::HTTP_OK == $response->getCode()) {
                $data = (array) json_decode($response->getData(), true);
                $vehicles = $data['documents'][0]['vehicle'] ?? [];
                $vehicleModels = [];
                $vehicleIds = [];
                foreach ($vehicles as $vehicle) {
                    $vehicleIds[] = $vehicle['id'] ?? $vehicle['_id'];
                    $vehicleModels[] = $this->denormalizer->denormalize($vehicle, VehicleModel::class);
                }

                $this->markOrdersAsRead($userId, $vehicleIds);

                return new SuccessResponse(['vehicles' => $vehicleModels]);
            }
            $result = $response->getData();
            if (isset($result['error']['message'])) {
                $result = $result['error']['message'];
            }

            return new ErrorResponse($result, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.': Catched Exception VehicleManager::getVehicles '.$e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Set isUpdated to  false so the front knows the new orders from already seens ones.
     *
     * @return void
     */
    public function markOrdersAsRead(string $userId, array $vehicleIds)
    {
        foreach ($vehicleIds as $vehicleId) {
            $this->service->setOrderIsUpdated($userId, $vehicleId, false);
        }
    }

    /**
     * create or update vehicle data function.
     */
    public function createOrUpdateVehicle(array $content): ResponseArrayFormat
    {
        try {
            $userId = $content['userId'] ?? null;
            $vehicleModel = $this->getVehicleModel($content);
            $errorsMessages = $this->vehicleValuesValidate($vehicleModel);
            if (empty($userId)) {
                $errorsMessages[] = 'The user ID value should not be blank.';
            }
            if ($errorsMessages) {
                return new ErrorResponse($errorsMessages);
            }

            $response = $this->service->createOrUpdateVehicle($userId, $vehicleModel);
            if (Response::HTTP_OK == $response->getCode() || Response::HTTP_CREATED == $response->getCode()) {
                $data = [
                    'message' => 'Vehicle data has been saved successfully',
                    'mopId' => $content['mopId'],
                ];

                $spaceVehicleUpdatedEvent = new SpaceVehicleUpdatedEvent(
                    $userId,
                    $vehicleModel,
                    $content
                );
                $this->dispatcher->dispatch(
                    $spaceVehicleUpdatedEvent,
                    SpaceVehicleUpdatedEvent::class
                );
                if ($spaceVehicleUpdatedEvent->getResponse() instanceof ErrorResponse) {
                    $this->logger->error(
                        'An error has occurred while sync MyMarque vehicle data with Space',
                        [
                            'userId' => $userId,
                            'model' => $vehicleModel,
                            'error' => $spaceVehicleUpdatedEvent->getResponse()->toArray(),
                        ]
                    );
                }
                if ($spaceVehicleUpdatedEvent->getResponse() instanceof SuccessResponse) {
                    // adding logic to update the flag isUpdated to true for the vehicle
                }

                return new SuccessResponse($data);
            }
            $data = [
                'raison' => 'An error has occurred, vehicle data has not been saved',
                'mopId' => $content['mopId'],
            ];

            return new ErrorResponse($data);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__. ' Catched Exception '.$e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * check input errors.
     */
    public function vehicleValuesValidate(VehicleModel $vehicleModel): array
    {
        $errors = $this->validator->validate($vehicleModel);
        $messages = [];
        if (count($errors) > 0) {
            foreach ($errors as $error) {
                $messages[] = $error->getMessage();
            }
        }
        $errors = $this->validator->validate($vehicleModel->getVehicleOrder());
        if (count($errors) > 0) {
            foreach ($errors as $error) {
                $messages[] = $error->getMessage();
            }
        }

        return $messages;
    }

    /**
     * mapping content like a array data.
     */
    public function getVehicleModel(array $content): VehicleModel
    {
        $culture = CultureHelper::parseCulture($content['culture']);
        $content['country'] = $culture['country'];
        $content['language'] = $culture['language'];
        $content = $this->manageOVData($content);

        if (empty($content['visual'])) {
            $content['visual'] = BrandProvider::getBrandDefaultImage($content['brand']);
        }

        $content['vehicleOrder'] = [
            'mopId' => $content['mopId'] ?? '',
            'orderFormId' => strval($content['orderFormId'] ?? ''),
            'trackingStatus' => $content['trackingStatus'] ?? '',
            'orderFormStatus' => $content['orderFormStatus'] ?? '',
        ];

        return $this->serializer->deserialize(json_encode($content), VehicleModel::class, 'json');
    }

    /**
     * get vehicle data with GB cases.
     */
    public function manageOVData(array $vehicle): array
    {
        if ($this->service->isOVVehicle($vehicle['versionId'])) {
            $allAttributes = $this->service->getVinAttributes($vehicle['vin'], 'OP'); /* forcing OP brand */
            $brand = $vehicle['brand'] = $this->service->getVehicleBrand($allAttributes, $vehicle['country']);
            if ('VX' == $brand) {
                $vehicle['language'] = 'en';
                $vehicle['country'] = 'GB';
            }
        }

        return $vehicle;
    }

    /**
     * Get Vehicles Summary.
     */
    public function getVehicleSummary(string $vehicleId, string $userId): ResponseArrayFormat
    {
        try {
            $vehicle = $this->getVehicleInfo($vehicleId, $userId);
            $vehicleOrder = $vehicle->getVehicleOrder();
            $brand = $vehicle->getBrand();
            $country = $vehicle->getCountry();
            if (empty($country)) {
                $country = 'AT';
            }
            $orderFormId = $vehicleOrder->getOrderFormId();
            $mopId = $vehicleOrder->getMopId();

            $response = $this->service->getOrderSummary($orderFormId, $mopId, $brand, $country);
            if (Response::HTTP_OK == $response->getCode()) {
                return new SuccessResponse(['url' => $response->getData()['success']['pdf_url'] ?? '']);
            }
            $errorMessage = $response->getData()['error']['message'] ?? 'server error';
            $this->logger->error(__METHOD__ .' Error response '.$errorMessage);

            return new ErrorResponse($errorMessage, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception '.$e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function getVehicleInfo(string $vehicleId, string $userId): VehicleModel
    {
        $pipeline =
            [
                [
                    '$match' => ['userId' => $userId],
                ],
                [
                    '$unwind' => '$vehicle',
                ],
                [
                    '$match' => [
                        'vehicle.id' => ['$eq' => $vehicleId],
                    ],
                ],
                [
                    '$group' => [
                        '_id' => 'vehiclesOrderforSummary',
                        'vehicle' => [
                            '$push' => '$vehicle',
                        ],
                    ],
                ],
            ];

        $response = $this->mongoService->aggregate(VehicleService::COLLECTION, $pipeline);
        $response = json_decode($response->getData(), true);

        $data = isset($response['documents']) ? reset($response['documents']) : [];
        $vehicle = isset($data['vehicle']) ? reset($data['vehicle']) : null;

        if (!$vehicle) {
            throw new \Exception('vehicle not found', Response::HTTP_NOT_FOUND);
        }
        $VehicleModel = $this->denormalizer->denormalize($vehicle, VehicleModel::class);

        return $VehicleModel;
    }

    public function getUserVehiclesData(
        string $userId, 
        string $brand, 
        string $language, 
        string $country
    ): ResponseArrayFormat
    {
        try{
            $filters = [
                'brand' => $brand,
                'country' => $country
            ];

            // retrieve user data from MongoDB, if exists
            $userDataDocument = $this->userDataService->getUserDataDocument($userId);
            $this->logger->debug(__METHOD__.' Initial user vehicles data retrieved', [
                'userId' => $userId,
                'userDataDocument' => $userDataDocument]);

            // if not exists, create new one
            if (null === $userDataDocument) {
                $userDataDocument = new UserDataDocument();
                $userDataDocument->userId = $userId;
            }

            // retrieve vehicles data for xF brands
            $xfVehicles = $this->xFVehicleRefreshService->retrieveVehicles($userDataDocument, $brand, $language, $country);

            // retrieve vehicles data for xP brands
            $xpVehicles = $this->xPVehicleRefreshService->retrieveVehicles($userDataDocument, $brand, $language, $country);

            $this->logger->debug(__METHOD__.' User vehicles data refreshed data retried', [
                'xfVehicles' => $xfVehicles,
                'xpVehicles' => $xpVehicles,
            ]);

            // Update all vehicles
            $stellantisVehicles = array_merge($xpVehicles, $xfVehicles);
            foreach ($stellantisVehicles as $vehicle) {
                Assert::isInstanceOf($vehicle, SystemVehicleData::class);
                
                // update vehicles data on MongoDB for the user
                $this->saveUserDataDocumentVehicles($userId, $vehicle->getVin(), $vehicle);
            }

            // for GSDP sdp remove all vehicles that are not in the list        
            $this->userDataService->removeUserGSPDVehicles($userId, $brand, array_keys($xfVehicles));
                       
            // read a fresh copy of vehicles data from MongoDB
            $userVehicles = $this->userDataService->getVehicleByUserIdAndBrand($userId, $brand);
            $this->logger->debug(__METHOD__.' User vehicles data refreshed successfully', [
                'userId' => $userId,
                'userDataDocument' => $userDataDocument
            ]);

            $vehiclesList = [];
            foreach ($userVehicles as $userVehicle) {
                Assert::isInstanceOf($userVehicle, Vehicle::class);                

                $vehicleListResponse = VehicleListResponseMapper::mapUserDataDocumentVehicle($userVehicle);
                $vehiclesList[] = $this->normalizer->normalize($vehicleListResponse, null, ['groups' => ['xp_vehicle_list']]); 
            }

            return new SuccessResponse($vehiclesList, Response::HTTP_OK);
            
        } catch (Throwable $e) {        
            $this->logger->error(__METHOD__.': Catched Exception', [
                'userId' => $userId,
                'brand' => $brand,
                'country' => $country,                
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return new ErrorResponse(
                'An error occurred while fetching user vehicles. '.$e->getMessage(),
                Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * insert or update vehicle data in user document.
     *
     * @return void
     */
    private function saveUserDataDocumentVehicles(string $userId, string $vin, SystemVehicleData $discoveredVehicle): void
    {
        $userVehicle = $this->userDataService->findUserVehicleByVin($userId, $vin);
        if(null === $userVehicle){
            // add the vehicle if it doesn't exist
            $userVehicle = VehicleMapper::mapSystemVehicleData($discoveredVehicle);
            $userVehicle->id = RefreshVehicleHelper::generateUid();
            $context = ['groups' => ['xFInsert', 'xFUpdate']];
            if(in_array($userVehicle->brand, $this->xPVehicleRefreshService::BRANDS_XP)){
                $context = ['groups' => ['xPInsert', 'xPUpdate']];
            }
            $this->userDataService->addVehicleToUserDocument($userId, $userVehicle, $context);

        } else {
            $context = ['groups' => ['xFUpdate']];
            if(in_array($userVehicle->brand, $this->xPVehicleRefreshService::BRANDS_XP)){
                $context = ['groups' => ['xPUpdate']];
            }

            // update the vehicle if it exists
            $updatedUserVehicle = VehicleMapper::mapSystemVehicleData($discoveredVehicle, $userVehicle);
            $this->logger->debug(__METHOD__.' Updating user vehicle data', [
                'discoveredVehicle' => $discoveredVehicle,
                'userVehicle' => $userVehicle,
                'updatedUserVehicle' => $updatedUserVehicle
            ]);

            $this->userDataService->updateVehicleInUserDocument($userId, $updatedUserVehicle, $context);
        }
    }

    public function handleVehicleContractUpdate (string $vin) 
    {
        try {
            $userDocument = $this->userDataService->getVehicleAndUserIdByVin($vin);
            if (!$userDocument) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__."No user data found for VIN: {$vin}");
                return false;
            }

            $userId = $userDocument['userId'];
            $vehicleObj = $userDocument['vehicle'];
            $vehicleType = $vehicleObj->getType();
            $lcdv = $vehicleObj->getVersionId();
            $brand = $vehicleObj->getBrand();
            $f2mcObj = $userDocument['f2mc'] ?? null;
            $country = $vehicleObj->getCountry();

            //Call corvet API
            // $corvetData = $this->getCorvetData($vin, $brand);
            $corvetData = $this->mockCorvetData(); //TODO remove this once corvet is implemented

            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->getManagedAttributes($allAttributes);

            $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, $f2mcObj, $country);

            if (empty($featureCodes)) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__."No feature codes generated for VIN: {$vin}, User ID: {$userId}");
                return false;
            }
    
            if (!$this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes)) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__."Failed to update feature codes for VIN: {$vin}, User ID: {$userId}");
                return false;
            }
    
            $this->logger->info(__CLASS__ . '::' . __METHOD__."Successfully updated feature codes for VIN: {$vin}, User ID: {$userId}");
            return true;
        } catch (Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__."Error", ['vin' => $vin, 'exception' => $e->getMessage()]);
            return false;
        }
    }

    public function addSSDPVehicle(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        try {
            if(!$this->brandHelper->isSsdpBrand($vehicleDto->getBrand())){
                $this->logger->error(__METHOD__ . ' : Error brand not XP');
                return new ErrorResponse($vehicleDto->getBrand() . ' Brand not supported', Response::HTTP_BAD_REQUEST);
            }
            $vehicle = $this->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
            if ($vehicle) {
                $this->logger->error(__METHOD__ . ' : Error vehicle already exist');
                return new ErrorResponse('Vehicle already exist', Response::HTTP_BAD_REQUEST);
            }

            $userDbId = $this->getUserDbId($vehicleDto->getUserId());
            if (!$userDbId) {
                $this->logger->error(__METHOD__ . ' : Error userDbId not found');
                return new ErrorResponse('User DB ID not found', Response::HTTP_NOT_FOUND);
            }
            $corvetData = $this->getCorvetData($vehicleDto->getVin(), $vehicleDto->getBrand());
            $lcdv = $this->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
            }
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
            $vehicleSettings = $this->findVehicleLabelByLcdv($lcdv);

            $label = $vehicleSettings['label'] ?? null;
            $isO2x = $vehicleSettings['isO2x'] ?? false;
            $visualUrl = $vehicleSettings['defaultImage'] ?? null;
            $sdp = $vehicleSettings['sdp'] ?? '';
            if (!$label) {
                $this->logger->error(__METHOD__ . ' : Error label not found while calling mongoDb vehicleLabel');
                return new ErrorResponse('Label Not found ', Response::HTTP_NOT_FOUND);
            }
            $params = [
                'vin' => $vehicleDto->getVin(),
                'brand' => $vehicleDto->getBrand(),
                'country' => $vehicleDto->getCountry(),
                'userId' => $vehicleDto->getUserId(),
                'commercialName' => $label,
                'pictureUrl' => $visualUrl
            ];
            $params = array_filter($params, function ($value) {
                return $value !== null;
            });
        $addVehicleInUserDbResponse = $this->systemUserDBManager->addVehicle($userDbId, $params);
        if ($addVehicleInUserDbResponse->getCode() !== Response::HTTP_OK) {
            $this->logger->error(__METHOD__ . ' : Error adding vehicle in user db');
            $data = $addVehicleInUserDbResponse->getData();
            $message = $data['error']['message'] ?? $data;
            $errorResponse = new ErrorResponse($message, $addVehicleInUserDbResponse->getCode());
            $errorResponse->setErrors(($data['error']['errors'] ?? []));
            return $errorResponse;
        }
            $vehicleOutputDto = new AddVehicleOutputDTO();
            $corvertAttributs = $this->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
            $vehicleOutputDto->setAddStatus('COMPLETE');
            $vehicleOutputDto->setSdp($sdp);
            $vehicleOutputDto->setWarrantyStartDate($vehicleData['VEH_WARRANTY_START_DATE'] ?? null);
            $vehicleOutputDto->setRegTimestamp(time());
            $vehicleOutputDto->setType($vehicleType);
            $vehicleOutputDto->setLabel($label);
            $vehicleOutputDto->setLcdv($lcdv);
            $vehicleOutputDto->setVisual($visualUrl);
            $vehicleOutputDto->setFeaturesCode($this->featureCodeService->getFeaturesCode( $userDbId, $vehicleDto->getVin(), $lcdv, $vehicleType, $corvertAttributs));
            $vehicleOutputDto->setIsOrder(false);
            $vehicleOutputDto->setMake($vehicleDto->getBrand());
            $vehicleOutputDto->setMarket($vehicleDto->getCountry());
            $vehicleOutputDto->setCulture(CultureHelper::createCulture($vehicleDto->getLanguage(),$vehicleDto->getCountry()));
            $vehicleOutputDto->setIsO2X($isO2x);
            $vehicleOutputDto->setLastUpdate(time());
            // calling mongoDb to push new vehicle
            $mongodbUpdateResponse = $this->updateVehicleData($vehicleDto, $vehicleOutputDto);
            if ($mongodbUpdateResponse->getCode() == Response::HTTP_OK) {
                $this->logger->info(__METHOD__ . ' : vehicle added successfully in the mongoDb');
                return new SuccessResponse("Vehicle added Successfully", Response::HTTP_CREATED);
            }
            $this->logger->error(__METHOD__ . " : Failed to add vehicle. Code: ". $mongodbUpdateResponse->getCode() .". Message: ". $mongodbUpdateResponse->getData()['error']['message'] ?? $mongodbUpdateResponse->getData());
            return new ErrorResponse($mongodbUpdateResponse->getData(), $mongodbUpdateResponse->getCode());
        } catch (\Exception $e)
        {
            $this->logger->error(__METHOD__ . " : Error adding vehicle in user db", ['vin' => $vehicleDto->getVin(), 'exception' => $e->getMessage()]);
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }


    public function mockCorvetData(): array
    {
        $array = [
            "ENTETE" => [
                "EMETTEUR" => "MYM_PREPROD"
            ],
            "VEHICULE" => [
                "@attributes" => [
                    "Existe" => "O"
                ],
                "DONNEES_VEHICULE" => [
                    "WMI" => "VYE",
                    "VDS" => "ATTEN0",
                    "VIS" => "SPU00024",
                    "VIN" => "ZARHBTTG3R9011445",
                    "LCDV_BASE" => "1JJPSYNZDFL0A0A0M0ZZGHFY",
                    "N_APV_PR" => [],
                    "ANNEE_MODELE" => "0A",
                    "MARQUE_COMMERCIALE" => "0J",
                    "DATE_DEBUT_GARANTIE" => [],
                    "DATE_ENTREE_COMMERCIALISATION" => "18/12/2024 17:23:00",
                    "LIGNE_DE_PRODUIT" => "JP"
                ],
                "LISTE_ATTRIBUTES_7" => [
                    "ATTRIBUT" => [
                        "D0000CD", "D0103CD", "D0202CD", "D0301CD", "D0400CD", "D0500CD", "D0600CD", "D0701CD", "D0800CD",
                        "D0900CD", "D1000CD", "D1100CD", "D1202CD", "D1301CD", "D1500CD", "D1701CD", "D1801CD", "D1901CD",
                        "D1C03CD", "D1D05CD", "D1E55CD", "D1F24CD", "D1G30CD", "D1H09CD", "D2501CD", "D2802CD", "D2900CD",
                        "D2A00CD", "D3201CD", "D4100CD", "D4401CD", "D4I00CD", "D4K00CD", "D5000CD", "D5102CD", "D5901CD",
                        "D5M04CD", "D5N08CD", "D5O01CD", "D6007CD", "D6100CD", "D6200CD", "D6302CD", "D6404CD", "D6508CD",
                        "D6602CD", "D6706CD", "D6803CD", "D6D03CD", "D6E01CD", "D6F02CD", "D6G03CD", "D6H05CD", "D6J00CD",
                        "D6K04CD", "D6L04CD", "D6N01CD", "D6O00CD", "D6Q01CD", "D6V07CD", "D6W02CD", "D6X03CD", "D7003CD",
                        "D7A01CD", "D7B02CD", "D7C02CD", "D7E00CD", "D7H01CD", "D7K02CD", "D7L00CD", "D7P02CD", "D7Q02CD",
                        "D7S02CD", "D7T02CD", "D7U01CD", "D7V02CD", "D7W02CD", "D7X00CD", "D7Z04CD", "DA300CD", "DA401CD",
                        "DA516CD", "DA639CD", "DA702CD", "DAB00CD", "DAE00CD", "DAF01CD", "DAGCDCD", "DAH02CD", "DAJ01CD",
                        "DAK06CD", "DAL45CD", "DAO05CD", "DAP01CD", "DAQ00CD", "DAR29CD", "DAS03CD", "DAU02CD", "DAZ10CD",
                        "DBF01CD", "DBJ03CD", "DBK60CD", "DBS00CD", "DBU11CD", "DCD00CD", "DCF14CD", "DCG18CD", "DCK04CD",
                        "DCL12CD", "DCN04CD", "DCO01CD", "DCP01CD", "DCQ06CD", "DCU22CD", "DCX01CD", "DD429CD", "DD606CD",
                        "DDA41CD", "DDC00CD", "DDD04CD", "DDE02CD", "DDG00CD", "DDH82CD", "DDI00CD", "DDJ03CD", "DDO01CD",
                        "DDR07CD", "DDT00CD", "DDX02CD", "DDY23CD", "DDZI7CD", "DE201CD", "DE301CD", "DE704CD", "DE803CD",
                        "DED44CD", "DEE37CD", "DEF00CD", "DEG23CD", "DEHEOCD", "DEJ06CD", "DEK08CD", "DEL01CD", "DENWWCD",
                        "DES03CD", "DEZZZCD", "DFG12CD", "DFH05CD", "DFI08CD", "DFT02CD", "DFU00CD", "DFX00CD", "DGA01CD",
                        "DGH01CD", "DGMAZCD", "DGQ22CD", "DGY08CD", "DGZ00CD", "DHB39CD", "DHE00CD", "DHG06CD", "DHJ00CD",
                        "DHM00CD", "DHU24CD", "DHY03CD", "DI202CD", "DI301CD", "DI402CD", "DI501CD", "DI604CD", "DI702CD",
                        "DI801CD", "DI901CD", "DIB01CD", "DIF10CD", "DIM18CD", "DIN07CD", "DIO02CD", "DIP03CD", "DIQ02CD",
                        "DIT14CD", "DIU16CD", "DIW00CD", "DJA25CD", "DJB04CD", "DJD02CD", "DJQ00CD", "DJY11CD", "DK303CD",
                        "DK906CD", "DKU03CD", "DKX41CD", "DL311CD", "DL600CD", "DL700CD", "DL801CD", "DL900CD", "DLA10CD",
                        "DLB13CD", "DLD00CD", "DLE12CD", "DLI16CD", "DLN03CD", "DLV02CD", "DLW02CD", "DLX54CD", "DLZ06CD",
                        "DMG08CD", "DMH00CD", "DMI61CD", "DMJAKCD", "DMO13CD", "DMW13CD", "DN100CD", "DN400CD", "DN510CD",
                        "DN706CD", "DN803CD", "DN904CD", "DNA09CD", "DNB08CD", "DNC05CD", "DNF15CD", "DNG00CD", "DNH01CD",
                        "DNK05CD", "DNM00CD", "DNN01CD", "DNR00CD", "DO103CD", "DO301CD", "DO409CD", "DO506CD", "DO813CD",
                        "DO906CD", "DOA04CD", "DOCADCD", "DOD02CD", "DOK00CD", "DOL11CD", "DOP01CD", "DOR03CD", "DOS01CD",
                        "DOY25CD", "DPDZCCD", "DPKADCD", "DPLNVCD", "DPQ02CD", "DPR04CD", "DPS02CD", "DPY13CD", "DQA05CD",
                        "DQB48CD", "DQC00CD", "DQF00CD", "DQH20CD", "DQJ04CD", "DQK15CD", "DQS10CD", "DQT00CD", "DQV03CD",
                        "DQX01CD", "DRA01CD", "DRC71CD", "DRE24CD", "DRG40CD", "DRH14CD", "DRI00CD", "DRJ05CD", "DRK05CD",
                        "DRP02CD", "DRQ01CD", "DRS19CD", "DRT21CD", "DRU20CD", "DRZ89CD", "DSB00CD", "DSD04CD", "DSH02CD",
                        "DSO01CD", "DSP16CD", "DTC00CD", "DTG09CD", "DTJ02CD", "DUB24CD", "DUC00CD", "DUE05CD", "DUF01CD",
                        "DUR00CD", "DUV37CD", "DUW00CD", "DUZ19CD", "DVD09CD", "DVF37CD", "DVH37CD", "DVKAICD", "DVO01CD",
                        "DVQ72CD", "DVS05CD", "DVU01CD", "DVW00CD", "DVX23CD", "DWAICCD", "DXA00CD", "DXC11CD", "DXD04CD",
                        "DXF00CD", "DXG24CD", "DXQAZCD", "DXU00CD", "DXZ01CD", "DYB01CD", "DYC00CD", "DYE01CD", "DYH02CD",
                        "DYI45CD", "DYK13CD", "DYM25CD", "DYP00CD", "DYQ02CD", "DYR22CD", "DYT31CD", "DYU03CD", "DYV19CD",
                        "DYW25CD", "DZE34CD", "DZICUCD", "DZJFVCD", "DZVNICD", "DZZ0JCD", "T1AADG", "T1BADG", "T9AADG",
                        "T9BADG", "T9CADG", "T9DADG", "NEW1CD", "NEW2CD"
                    ]
                ]
            ]
        ];
        return $array;
    }

    public function addXPVehicle(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        if(!$this->brandHelper->isSsdpBrand($vehicleDto->getBrand())){
            $this->logger->error(__METHOD__ . ' : Error brand not SSDP');
            return new ErrorResponse('Brand not supported', Response::HTTP_BAD_REQUEST);
        }
        $vehicle = $this->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
        if ($vehicle) {
            $this->logger->error(__METHOD__ . ' : Error vehicle already exist');
            return new ErrorResponse('Vehicle already exist', Response::HTTP_BAD_REQUEST);
        }
        // call corvet
        $corvetData = $this->getCorvetData($vehicleDto->getVin(), $vehicleDto->getBrand());
        //retrive lcdv
        $lcdv = $this->getLcdv($corvetData);
        if (!$lcdv) {
            $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
            return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
        }
        // 
        $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
        $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
        $vehicleSettings = $this->findVehicleLabelByLcdv($lcdv);
        //call mongoDb vehicleLabel to get label
        $label = $vehicleSettings['label'] ?? null;
        $isO2x = $vehicleSettings['isO2x'] ?? false;
        if (!$label) {
            $this->logger->error(__METHOD__ . ' : Error label not found while calling mongoDb vehicleLabel');
            return new ErrorResponse('Label Not found ', Response::HTTP_NOT_FOUND);
        }
        $visualResponse = $this->visual3DManager->loadImages(
            $lcdv,
            $vehicleDto->getBrand(),
            $vehicleDto->getSource(),
            $this->getDataFromAttributes($allAttributes)
        );

        $visualUrl = $visualResponse['data'] ?? null;

        //call mongodb to get psaId = ACNT
        $psaId = $this->getPsaId($vehicleDto->getUserId(), $vehicleDto->getBrand());
        if (!$psaId) {
            $this->logger->error(__METHOD__ . ' : Error psaId not found while calling mongoDb userData');
            return new ErrorResponse('PsaId not found ', Response::HTTP_NOT_FOUND);
        }

        $siteCode = RefreshVehicleHelper::getSiteCode($vehicleDto->getBrand(), $vehicleDto->getCountry(), $vehicleDto->getSource());
        //call C@ to get ticket
        $ticket = $this->getTicket($psaId, $siteCode);
        if (!$ticket) {
            $this->logger->error(__METHOD__ . ' : Error Ticket not found while calling C@');
            return new ErrorResponse('Ticket not found ', Response::HTTP_NOT_FOUND);
        }

        // add vehicle to customer@
        $response = $this->addVehicleInCustomerAt(
            $vehicleDto->getVin(),
            $label,
            $lcdv,
            $ticket,
            $siteCode,
            $vehicleDto->getLanguage()
        );

        if (!$response) {
            $this->logger->error(__METHOD__ . ' : Error while adding vehicle in C@ VIN : ' . $vehicleDto->getVin());
            return new ErrorResponse('Error while adding vehicle in C@ ', Response::HTTP_BAD_REQUEST);
        }
        $vehicleOutputDto = new AddVehicleOutputDTO();
        $corvertAttributs = $this->getManagedAttributes($allAttributes);
        $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
        $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
        $vehicleOutputDto->setAddStatus('COMPLETE');
        $sdp = $this->getSdp($vehicleSettings['sdp']);
        $vehicleOutputDto->setSdp($sdp);
        $vehicleOutputDto->setWarrantyStartDate($vehicleData['DATE_DEBUT_GARANTIE'] ?? '');
        $vehicleOutputDto->setRegTimestamp(time());
        $vehicleOutputDto->setType($vehicleType);
        $vehicleOutputDto->setLabel($label);
        $vehicleOutputDto->setLcdv($lcdv);
        $vehicleOutputDto->setVisual($visualUrl);
        // $vehicleOutputDto->setFeaturesCode($this->featureCodeService->getFeaturesCode($lcdv, $corvertAttributs));
        $vehicleOutputDto->setIsOrder(false);
        $vehicleOutputDto->setMake($vehicleDto->getBrand());
        $vehicleOutputDto->setMarket($vehicleDto->getCountry());
        $vehicleOutputDto->setCulture(CultureHelper::createCulture($vehicleDto->getLanguage(),$vehicleDto->getCountry()));
        $vehicleOutputDto->setIsO2X($isO2x);
        $vehicleOutputDto->setLastUpdate(time());
        // calling mongoDb to push new vehicle
        $this->updateVehicleData($vehicleDto, $vehicleOutputDto);
        return new SuccessResponse("Vehicle added Successfully", Response::HTTP_CREATED);
    }

    public function editVehicle(EditVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        try {
            $vehicle = $this->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
            if(!$vehicle) {
                return new ErrorResponse('Vehicle not exists', Response::HTTP_NOT_FOUND);
            }
            $sdp = $vehicle['vehicle'][0]['sdp'] ?? null;
            $vehicleDto->setCommercialName($vehicle['vehicle'][0]['shortLabel'] ?? '');
            $vehicleDto->setPictureUrl($vehicle['vehicle'][0]['picture'] ?? '');
            if($sdp != RefreshVehicleInterface::SDP_SSDP) {
                return new ErrorResponse('Vehicle not managed in user repository', Response::HTTP_NOT_FOUND);
            }
            $customerId = $vehicle['userDbId'] ?? null;
            if(!$customerId) {
                return new ErrorResponse('User not existe in user repository', Response::HTTP_NOT_FOUND);
            }
            $vehicleDto->setCustomerId($customerId);
            $response = $this->systemUserDBService->updateCustomerGarage($vehicleDto);
            if($response->getCode() == Response::HTTP_OK) {
                $vehicleDto->setMileageDate(time());
                $vehicleDto->setMileage($vehicleDto->getMileage() !== null ? (int) $vehicleDto->getMileage():($vehicle['vehicle'][0]['mileage']['value'] ?? 0));
                $vehicleDto->setNickName($vehicleDto->getNickName() ?:($vehicle['vehicle'][0]['nickName'] ?? ''));
                $vehicleDto->setLicencePlate($vehicleDto->getLicencePlate() ?:($vehicle['vehicle'][0]['licencePlate'] ?? ''));
                $this->updateVehicle($vehicleDto);
                return new SuccessResponse(EditVehicleMapper::map($vehicleDto), Response::HTTP_OK);
            }
            return (new ErrorResponse($response->getData()['error']['message'] ?? '', $response->getCode()))->setErrors($response->getData()['error']['errors'] ?? []);
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.'Catched Exception VehicleManager::editVehicle '.$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function getSdp(?string $vehicleLabelSdp = null){
        $sdp = RefreshVehicleInterface::SDP_CVMP;
        if($vehicleLabelSdp == RefreshVehicleInterface::SDP_SSDP) {
            $sdp = RefreshVehicleInterface::SDP_SSDP;
        }

        return $sdp;
    }

    public function setEligibilityFromContracts(?array $subscriptions): array
    {   
        $eligibilities = [];
        foreach ($subscriptions as $subscription) {

            $eligibility = $subscription['type'] ?? '';
            if (in_array($eligibility, ['remotelev_phev','remotelev_bev', 'bev', 'phev'])) {
                $eligibility = self::ELIGIBILITY_REMOTELEV;
            } 
            elseif ($eligibility === 'navco') {
                $eligibility = self::ELIGIBILITY_NAC; 
            }

            if ($eligibility && !in_array($eligibility, $eligibilities)) {
                $eligibilities[] = $eligibility;
            }
        }
        return $eligibilities;
    }

    public function getVehicleType(string $vehicleType): int
    {
        switch (strtoupper($vehicleType)) {
            case "ICE":
                return 0;
            case "HEV":
                return 2;
            case "PHEV":
                return 3;
            case "BEV":
                return 4;
            case "MHEV":
                return 5;
            case "HFCV":
                return 6;
            default:
                return 10;
        }
    }
    
    public function removeNullValues(array $array): array 
    {
        foreach($array as $key => $value) {
            if(is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            }
            if(is_null($array[$key])) {
                unset($array[$key]);
            }
        }        
        return $array;
    }

    /**
     * {
     *   "success": {
     *     "vehicleInfo": {
     *       "vin": "VF30U9HD8ES290122",
     *       "lcdv": "1PTBSYHBM604A0F1M09P05FX",
     *       "visual": "https://visuel3d-secure.peugeot.com/V3DImage.ashx?client=MyMarque&format=png&color=0MM00N9P&trim=0P050RFX&back=0&width=1000&version=1PTBSYHBM604A0F1&view=001&OPT1=VD09&OPT2=WLWF&OPT3=ZD09&OPT4=ZH47",
     *       "short_label": "3008",
     *       "warranty_start_date": 1423612800,
     *       "attributes": [
     *         "DCD06CD",
     *         "DCW79CD",
     *         "DCX01CD",
     *         "DJY05CD",
     *         "DLX15CD",
     *         "DRCT6CD",
     *         "DRE07CD",
     *         "DVQ21CD",
     *         "DXD00CD"
     *       ],
     *       "type_vehicle": 0,
     *       "mileage": {
     *         "value": 0
     *       }
     *     },
     *     "eligibility": [
     *       "device_smeg",
     *       "smartappsv1"
     *     ],
     *     "vehicleProducts": {
     *       "productsCatalog": [],
     *       "purchasedProducts": [],
     *       "productGroupNameStatus": {}
     *     },
     *     "settingsUpdate": 1736851272
     *   }
     * }
     */
    public function mapVehicleInfoXPFormat(array $vehicle, array $catalogResponse, array $subscriptionResponse, array $productsStatus): array
    {
        $type = $this->getVehicleType($vehicle['type'] ?? '');
        $vehicleXPFormat = [
            'vehicleInfo' => [
                'vin' => $vehicle['vin'] ?? '',
                'lcdv' => $vehicle['lcdv'] ?? '',
                'visual' => $vehicle['picture'] ?? '',
                'short_label' => $vehicle['nickName'] ?? $vehicle['shortLabel'],
                'nickname' => $vehicle['nickName'] ?? '',
                'warranty_start_date' => $vehicle['warrantyStartDate'] ?? null,
                'attributes' => $vehicle['attributes'] ?? [],
                'type_vehicle' => $type,
                'mileage' => $vehicle['mileage'] ?? new stdClass(),
                'versionId' => $vehicle['versionId'] ?? '',
                'brand' => $vehicle['make'] ?? '',
                'subMake' => $vehicle['subMake'] ?? '',
                'country' => $vehicle['market'] ?? '',
                'regTimeStamp' => $vehicle['regTimeStamp'] ?? '',
                'year' => $vehicle['year'] ?? '',
                'sdp' => $vehicle['sdp'] ?? '',
                'isOrder' => $vehicle['isOrder'] ?? '',
                'features' => $vehicle['featureCode'] ?? new stdClass(),
                'addStatus' => $vehicle['addStatus'] ?? '',
                'isO2x' => $vehicle['isO2x'] ?? ''
            ],
            'eligibility' => $this->setEligibilityFromContracts($subscriptionResponse),
            'vehicleProducts' => [
                'productsCatalog' => $catalogResponse,
                'purchasedProducts' => $subscriptionResponse,
                'productGroupNameStatus' => empty($productsStatus)? new stdClass() : $productsStatus
            ],
            'settingsUpdate' => $vehicle['lastUpdate'] ?? null
        ];

        $vehicleXPFormat = $this->removeNullValues($vehicleXPFormat);
        return $vehicleXPFormat;
    }

    public function getVehicleDetail(
        string $userId, 
        string $critariaValue, 
        string $language, 
        string $country, 
        string $critariaKey = 'vin',
        string $source = 'APP'
    ) {
        try {
            $vehicleResponse = $this->getVehicle($userId, $critariaValue, $critariaKey);
            
            if (!$vehicleResponse) {
                $this->logger->error(__METHOD__ . ' : Error vehicle not exist');
                return new ErrorResponse('Vehicle not exist', Response::HTTP_NOT_FOUND);
            }

            $vehicle = $vehicleResponse['vehicle'][0] ?? [];
            $sdp = $vehicle['sdp'] ?? '';
            $vin = $vehicle['vin'] ?? '';
            $brand = $vehicle['brand'] ?? '';
            $userDbId = $vehicleResponse['userDbId'] ?? null;
            
            if (!$userDbId)
            {
                $this->logger->error(__METHOD__ . ' : Error userDbId not exist');
                return new ErrorResponse('UserDbId not exist', Response::HTTP_NOT_FOUND);
            }
            
            $corvetData = $this->getCorvetData($vin, $brand);
            if (array_key_exists('error', $corvetData)) {
                $this->logger->error(__METHOD__ . ' : Error while calling corvet service ' . $corvetData['error']);
                return new ErrorResponse('Error while calling corvet service ' . $corvetData['error'], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
            $lcdv = $this->getLcdv($corvetData);
            $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->getManagedAttributes($allAttributes);
            $vehicleSettings = $this->findVehicleLabelByLcdv($lcdv);
            $isO2x = $vehicleSettings['isO2x'] ?? false;
            $label = $vehicleSettings['label'] ?? '';
            $vehicle['label'] = $label;
            $vehicle['lcdv'] = $lcdv;
            $vehicle['isO2x'] = $isO2x;
            $vehicle['attributes'] = $corvertAttributs;
            $params = [
                'userId' => $userId,
                'vin' => $vin,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'source' => $source,
                'userDbId' => $userDbId
            ];
            $target = 'B2C';

            $catalogResponse = $this->catalogManager->getCatalog($params);
            if ($catalogResponse->getCode() !== Response::HTTP_OK)
            {
                $this->logger->error(__METHOD__ . ' : Error while calling catalog service');
                return $catalogResponse;
            }
            $catalogResponse =  $catalogResponse->getData() ?? [];
            $productsStatus = array_fill_keys(array_column($catalogResponse, 'id', 'id'), 'disabled');

            $subscriptionResponse = $this->subscriptionManager->getSubscription($userDbId, $vin, $target, $brand, $country, $language, $source);
            if ($subscriptionResponse->getCode() !== Response::HTTP_OK)
            {
                $this->logger->error(__METHOD__ . ' : Error while calling subscription service');
                return $subscriptionResponse;
            }
            $subscriptionResponse = $subscriptionResponse->getData() ?? [];
            $productsStatus = array_merge($productsStatus ,array_fill_keys(array_map('strtolower', array_column($subscriptionResponse, 'type', 'type')), 'enabled'));


            $vehicleXPFormat = $this->mapVehicleInfoXPFormat($vehicle, $catalogResponse, $subscriptionResponse, $productsStatus);
            return new SuccessResponse($vehicleXPFormat ?? [], Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.'Catched Exception VehicleManager::getVehicleDetail '.$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function updateVehicleData(
        AddVehicleInputDTO $vehicleDto,
        AddVehicleOutputDTO $vehicleOutputDto
    ): WSResponse {
        $updatedVehicle['vehicle'] = [
            "id" => RefreshVehicleHelper::generateUid(),
            "versionId" => $vehicleOutputDto->getLcdv(),
            "shortLabel" => $vehicleOutputDto->getLabel(),
            "regTimeStamp" => $vehicleOutputDto->getRegTimestamp(),
            "year" => $vehicleOutputDto->getYear(),
            "vin" => $vehicleDto->getVin(),
            "modelDescription" => $vehicleOutputDto->getLabel(),
            "type" => $vehicleOutputDto->getType(),
            "mileage" => [
                "value" => (int) $vehicleDto->getMileage(),
                "date" => time()
            ],
            "nickName" => $vehicleOutputDto->getNickName(),
            "sdp" => $vehicleOutputDto->getSdp(),
            "featureCode" => $vehicleOutputDto->getFeaturesCode(),
            "picture" => $vehicleOutputDto->getVisual(),
            "brand" => $vehicleDto->getBrand(),
            "country" => $vehicleDto->getCountry(),
            "isOrder" => $vehicleOutputDto->getIsOrder(),
            "addStatus" => $vehicleOutputDto->getAddStatus(),
            "lastUpdate" => $vehicleOutputDto->getLastUpdate(),
            "make" => $vehicleOutputDto->getMake(),
            "market" => $vehicleOutputDto->getMarket(),
            "warrantyStartDate" => $vehicleOutputDto->getWarrantyStartDate(),
            "isO2x" => $vehicleOutputDto->getIsO2X(),
        ];
        
        $mongoResponse = $this->mongoService->updatePush(
            'userData',
            [
                'userId' => $vehicleDto->getUserId()
            ],
            $updatedVehicle
        );
        return $mongoResponse;
    }

    private function addVehicleInCustomerAt(
        string $vin,
        string $label,
        string $lcdv,
        string $ticket,
        string $siteCode,
        string $language
    ): ?array {

        $data = [
            'VEH_VIN' => $vin,
            'VEH_CLASS_LABEL' => $label,
            'VEH_LCDV' => $lcdv,
            'VEH_VIS' => substr($vin, 9),
        ];
        // insert into- customer@
        $response = $this->systemUserDataClient->addVehicle($ticket, $siteCode, $language, $data);

        return $response->getData()['success'] ?? null;
    }

    /**
     * @param string $psaId
     * @param string $siteCode
     * 
     * @return string|null
     */
    private function getTicket(
        string $psaId,
        string $siteCode
    ): ?string {
        // get ticket from user data
        $userdata = $this->systemUserDataClient->getV1CatTicket($psaId, $siteCode);
        return $userdata->getData()['success']['ticket'] ?? null;
    }

    /**
     * @param string $userId
     * @param string $brand
     * 
     * @return string|null
     */
    private function getPsaId(
        string $userId,
        string $brand
    ): ?string {

        $projection = [
            'userPsaId.$' => 1
        ];
        $userData = $this->findInMongoBy(
            'userData',
            [
                'userId' => $userId,
                'userPsaId.brand' => $brand
            ],
            $projection
        );
        return RefreshVehicleHelper::parsePsaId($userData['userPsaId'][0]['cvsId'] ?? '');
    }

   private function getLcdv(
    ?array $corvetData
   ): ?string {
        try{
            return $corvetData['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'] ?? null;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' : Error while getting lcdv from corvet data', ['corvetData' => $corvetData]);   
            return null;
        }
   }
   
   private function getCorvetData(
       string $vin,
       string $brand
   ): ?array {
       return $this->corvetService->getData($vin, $brand);
   }

    private function getVehicle(string $userId, string $critariaValue, string $critariaKey = 'vin'): ?array
    {
        $filter = [
            'userId' => $userId,
            'vehicle' => [
                '$elemMatch' => [
                    $critariaKey => $critariaValue,
                    'sdp' => 'SSDP'
                ]
            ]
        ];
    
        $projection = [
            'vehicle.$' => 1,
            'userDbId' => 1,
        ];
        
        $vehicle = $this->findInMongoBy(
            'userData',
            $filter,
            $projection
        );

        return $vehicle ?? null;
    }

    private function getUserDbId(string $userId): ?string
    {
        $filter = [
            'userId' => $userId,
        ];
    
        $projection = [
            'userDbId' => 1,
        ];
        
        $userDbId = $this->findInMongoBy(
            'userData',
            $filter,
            $projection
        );

        return $userDbId['userDbId'] ?? null;
    }

   private function findInMongoBy(
       string $collection,
       array $filters,
       ?array $projection = []
   ): ?array {
       $response = $this->mongoService->find(
           $collection,
           $filters,
           $projection
       );

       return json_decode($response->getData(), true)['documents'][0] ?? [];
   }

   private function findVehicleLabelByLcdv(?string $lcdv): ?array
   {
        $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
        return json_decode($response->getData(), true)['documents'][0] ?? [];
   }

   public static function getDataFromAttributes(array $attributes): ?array
   {
       $data  = [];
       foreach ($attributes as $attribute) {
           if (preg_match('/^P(.{4})|^D(.{4})CP$/i', $attribute)) {
               $data[] = substr($attribute, 1, 4);
           }
       }

       return $data;
   }

    private function getManagedAttributes(array $attributes): ?array
    {
        $managedAttributes  = [];
        foreach ($attributes as $attribute) {
            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DO9':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D32':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DYR':
                    if (substr($attribute, 3, 2) == "17") {
                        $managedAttributes[] = trim($attribute);
                    }
                    break;
            }
        }
        return $managedAttributes;
    }

    private function filterByCriteria(array $vehicles, string $critariaValue, string $critariaKey = 'vin'): ?array {
        $data = current(array_filter($vehicles, function($vehicle) use($critariaValue, $critariaKey) {
            return $vehicle[$critariaKey] == $critariaValue;
        })) ?? [];
        return $data;
    }

    private function updateVehicleDetailFromSdprData(
        AddVehicleOutputDTO $vehicle,
        string $userId,
        string $critariaValue,
        string $critariaKey
    ) {
        $updatedVehicle = [
            "vehicle.$.year" => $vehicle->getYear(),
            "vehicle.$.nickName" => $vehicle->getNickName(),
            "vehicle.$.regTimestamp" => $vehicle->getRegTimestamp(),
        ];

        return $this->mongoService->updateOne(
            'userData',
            [
                'userId' => $userId,
                'vehicle.'.$critariaKey => $critariaValue
            ],
            $updatedVehicle
        );
    }

    private function updateVehicle(
        EditVehicleInputDTO $vehicleDto
    ) {
        $updatedVehicle = [
            "vehicle.$.vin" => $vehicleDto->getVin(),
            "vehicle.$.mileage" => [
                "value" => $vehicleDto->getMileage(),
                "date" => $vehicleDto->getMileageDate()
            ],
            "vehicle.$.nickName" => $vehicleDto->getNickName(),
            "vehicle.$.licencePlate" => $vehicleDto->getLicencePlate()
        ];

        $this->mongoService->updateOne(
            'userData',
            [
                'userId' => $vehicleDto->getUserId(),
                'vehicle.vin' => $vehicleDto->getVin()
            ],
            $updatedVehicle
        );
    }


    /**
     * create or update vehicle data function.
     */
    public function deleteVehicle(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            $dbUserData = $this->userDataService->getVehicleAndUserDBIdByUserIdAndVin($userId, $vin);
            if($dbUserData != null && isset($dbUserData)) {
                $deleteResponse = $this->userDataService->removeUserSSDPVehicles($userId, $vin);
                if ($deleteResponse) {
                    $this->logger->info(
                        'Deleted successfully.'
                    );
                    $userDBResponse = $this->systemUserDBService->deleteCustomerVehicle($dbUserData["userDbId"], $vin);
                    if(Response::HTTP_OK !== $userDBResponse->getCode() && Response::HTTP_NO_CONTENT !== $userDBResponse->getCode()) {
                        $this->logger->error(
                            'An error has occurred while deleting Vehicle from User in SSDP',
                            [
                                'userDbId' => $dbUserData["userDbId"],
                                'vin' => $vin
                            ]
                        );
                        $this->userDataService->addVehicleToUserDocument($userId, $dbUserData["vehicle"]);
                        return new ErrorResponse("Error while deleting the vin in SSDP. userId ".$userId." vin ".$vin);
                    }
                    return new SuccessResponse("", Response::HTTP_NO_CONTENT);
                }
            }
            $data = [
                'reason' => 'An error has occurred, vehicle data has not been deleted',
                'userId' => $userId,
            ];

            return new ErrorResponse($data);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__. ' Catched Exception '.$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
