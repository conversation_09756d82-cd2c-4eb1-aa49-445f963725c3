<?php

namespace App\MongoDB\UserData\UserDataDocument;

use Symfony\Component\Serializer\Attribute\SerializedName;

class UserDataDocument
{
    #[SerializedName("_id")]
    public array|string $id;
    public string $userId;

    /**
     * @var Vehicle[]
     */
     #[SerializedName("vehicle")]
    public array $vehicles = [];

    /**
     * @var null|UserPsaId[]
     */
    public ?array $userPsaId = null;
}
