<?php

namespace App\MongoDB\UserData\UserDataDocument;

use Symfony\Component\Serializer\Attribute\Groups;

class Vehicle
{
    #[Groups(['xPInsert', 'xFInsert'])]
    public string $id = '';

    #[Groups(['xPInsert', 'xFInsert'])]
    public string $vin;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $shortLabel = null;

    #[Groups(['xFUpdate'])]
    public ?string $nickName = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $modelDescription = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $versionId = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $type = null;

    #[Groups(['default'])]
    public ?string $culture = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $brand = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $country = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $picture = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $sdp = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?int $regTimeStamp = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $enrollmentStatus = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $connectorType = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $make = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $subMake = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $market = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $year = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?int $lastUpdate;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $warrantyStartDate = null;

    #[Groups(['default'])]
    public ?bool $isOrder = null;

    #[Groups(['default'])]
    public ?VehicleOrder $vehicleOrder = null;

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getVersionId(): ?string
    {
        return $this->versionId;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }
}
