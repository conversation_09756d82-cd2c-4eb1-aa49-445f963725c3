<?php

namespace App\Service;

use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;

class ContribService
{
    use LoggerTrait;

    public function __construct(
        private SysSamsDataConnector $connector
    ) {
    }

    public function getContrib(string $brand, string $culture, string $productId, ?string $source = 'APP'): array
    {
        $options = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
            ]
        ];
        $url = '/v1/sams/contrib/getInfoByProduct/' . $productId;
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);
        if ($response->getCode() !== 200) {
            $data = $response->getData();
            if (isset($data["error"])) {
                throw new \Exception("Error calling API: ".$data["error"]["message"], $data["error"]["code"]);
            }
            throw new \Exception("Error calling API: ".$data, $response->getCode());
        }
        $contribData = $response->getData()['success'];
        return $contribData;
    }

    public function getContribInfoByProductIds(string $productIds, string $brand, string $culture, ?string $source = 'APP'): array
    {
        $options = [
            'query' => [
                'brand' => $brand,
                'culture' => $culture,
                'source' => $source,
                'productIds' => $productIds
            ]
        ];
        $url = '/v1/sams/contrib/getInfoByProduct';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", $options);

        $response = $this->connector->call(Request::METHOD_GET, $url, $options);
        if ($response->getCode() !== 200) {
            $data = $response->getData();
            if (isset($data["error"])) {
                throw new \Exception("Error calling API: ".$data["error"]["message"], $data["error"]["code"]);
            }
            throw new \Exception("Error calling API: ".$data, $response->getCode());
        }
        $contribData = $response->getData()['success'];
        return $contribData;
    }
}