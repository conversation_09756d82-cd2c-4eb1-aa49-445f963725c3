<?php 

namespace App\Service;

use App\Helper\VehicleTypeEntities;
use App\Manager\VehicleManager;
use App\Manager\Visual3DManager;
use App\Model\SystemVehicleData;
use App\MongoDB\UserData\UserDataDocument\UserDataDocument;
use App\Service\SystemUserDataClient;
use App\Service\UserDataService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Throwable;
use Webmozart\Assert\Assert as WebMozartAssert;

/**
 * XPVehicleRefreshService retrieve xP brands vehicles data.
 */
class XPVehicleRefreshService implements RefreshVehicleInterface
{
    use LoggerTrait;
    const BRANDS_TO_SKIP = ['SP'];
    

    public function __construct(
        private UserDataService $userDataService,
        private SystemUserDataClient $systemUserDataClient,
        private VehicleLabelService $vehicleLabelService,
        private ValidatorInterface $validator,
        private CorvetService $systemCorvetDataClient,
        private Visual3DManager $visual3DManager
    )
    {
    }

    private function findVehicleLabelByLcdv(?string $lcdv): ?array
   {
        $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
        return json_decode($response->getData(), true)['documents'][0] ?? [];
   }
        
    /**
     * @return array<SystemVehicleData>
     */
    public function retrieveVehicles(UserDataDocument $userDataDocument, string $brand, string $language, string $country): array
    {
        try{
            // skip requests for not xP brands
            if (!in_array($brand, self::BRANDS_XP)) {
                return [];
            }

            $vehiclesData = [];
            $userId = $userDataDocument->userId;

            // retry the list of ACNT codes form MongoDB
            $acntListAll = $this->userDataService->fetchACNTCodes($userDataDocument, self::BRANDS_TO_SKIP);
            $acntList = $this->filterAcntList($acntListAll, ['brand' => $brand]);

            if (empty($acntList)) {
                $this->logger->error(__METHOD__.' No ACNT codes found for the user', [
                    'userId' => $userId,
                    'brand' => $brand,                    
                    'acntListAll' => $acntListAll,
                    'acntList' => $acntList]);
                throw new \Exception('No ACNT codes found for the user');
            }

            // obtain Customer@ ticket
            $acntCode = reset($acntList);
            $brand = key($acntList);
            $cAtTicket = $this->obtainCustomerTicket($acntCode, $brand);

            // use ticketId to get vehicles details
            $vehiclesData = $this->fetchXPVehiclesDetails($userId, $cAtTicket, $brand, $language, $country);

            foreach ($vehiclesData as $vin => $singleVehicleData) {
                WebMozartAssert::isInstanceOf($singleVehicleData, SystemVehicleData::class);

                // retry Corvet data
                $vehiclesData[$vin] = $this->collectCorvetData($vin, $brand, $singleVehicleData);

                // retry label according to lcdv code from AtlasMongoDB
                $vehiclesData[$vin] = $this->lookUpLCDVLabels($singleVehicleData);

                // calculate image URL using visual settings
                // $filterCorvetAttribute = VehicleManager::getDataFromAttributes($singleVehicleData->getCorvetAttributes());
                // $imageUrl = $this->calculateImageUrl($singleVehicleData->getLcdv(), $brand, $filterCorvetAttribute);
                // $vehiclesData[$vin]->setImageUrl($imageUrl);
                $vehicleSettings = $this->findVehicleLabelByLcdv($singleVehicleData->getLcdv());
                $imageUrl = $vehicleSettings['defaultImage'] ?? null;
                $vehiclesData[$vin]->setImageUrl($imageUrl);                
            }
            return $vehiclesData;
        } catch (Throwable $e) {
            $this->logger->error(__METHOD__.' Error while retrieving XP user vehicles', [
                'userId' => $userDataDocument->userId, 
                'brand' => $brand,
                'country' => $country,
                'exception' => $e]);
            
            throw $e;
        }
    }

    public function transformImageUrl(?string $imageUrl): ?string
    {
        if (null === $imageUrl) {
            return null;
        }

        $parsedUrl = parse_url($imageUrl);

        $newUrl = '';
        if (isset($parsedUrl['query'])) {
            $newUrl .= '?' . $parsedUrl['query'];
        }
        if (isset($parsedUrl['fragment'])) {
            $newUrl .= '#' . $parsedUrl['fragment'];
        }

        return $newUrl;
    }

    private function filterAcntList(array $acntList, array $filters = []): array
    {
        if(!isset($filters['brand'])) {
            return $acntList;
        }

        $filteredAcntList = [];
        foreach ($acntList as $brand => $acntCode) {
            if ($filters['brand'] === $brand) {
                $filteredAcntList[$brand] = $acntCode;
            }
        }

        return $filteredAcntList;
    }

    private function obtainCustomerTicket(string $acntCode, string $brand): string
    {
        $siteCode = strtoupper(trim($brand)).'_FR_ESP';
        
        $response = $this->systemUserDataClient->getV1CatTicket($acntCode, $siteCode);

        if(Response::HTTP_OK != $response->getCode()) {
        }

        $data = $response->getData();

        $errors = $this->validator->validate($data, $this->getV1CatTicketConstraints());
        if (count($errors) > 0) {
            $this->logger->error(__METHOD__ . ' Error while obtaining Customer@ ticket', [
                'acntCode' => $acntCode, 'errors' => $errors]);
        }

        return $data['success']['ticket'];
    }

    /**
     * @return array<SystemVehicleData>
     */
    private function fetchXPVehiclesDetails(string $userId, string $cAtTicket, string $brand, string $language, string $country): array
    {
        $siteCode = $brand.'_'.$country.'_ESP';
        $response = $this->systemUserDataClient->getV1Vehicles($cAtTicket, $siteCode, $language);

        if(Response::HTTP_OK != $response->getCode()) {
            $this->logger->error(__METHOD__ . ' Error while obtaining vehicles information from Customer@', [
                'cAtTicket' => $cAtTicket, 
                'response' => $response
            ]);
            return [];
        }

        $this->logger->debug(__METHOD__ . ' Vehicles informatino from Customer@', [
            'cAtTicket' => $cAtTicket, 
            'data' => $response->getData()
        ]);

        $data = $response->getData();        
        $errors = $this->validator->validate($data, $this->getV1VehiclesConstraints());

        if (count($errors) > 0) {
            $this->logger->error(__METHOD__ . ' Impossible to retry vehicles information from Customer@', [
                'cAtTicket' => $cAtTicket,                 
                'errors' => $errors
            ]);

            return [];
        }

        $vehiclesData = $this->mapV1VehiclesToSystemVehicleData($data, [
            'userId' => $userId,
            'brand' => $brand]);

        return $vehiclesData;
    }

    private function collectCorvetData(string $vin, string $brand, SystemVehicleData $vehicleData): SystemVehicleData
    {
        $response = $this->systemCorvetDataClient->getDataWsResponse($vin, $brand);
        
        if(Response::HTTP_OK != $response->getCode()) {
            $this->logger->error(__METHOD__ . ' Error while obtainig Corvet data', [
                'vin' => $vin, 
                'code' => $response->getCode(),
                'error' => $response->getData()]);            
        }else{
            $this->logger->debug(__METHOD__ . ' Corvet data', [
                'vin' => $vin, 'data' => $response->getData()]);  

            // validate the response
            $data = $response->getData();
            $errors = $this->validator->validate($data, $this->getV1CorvetVinData());
            
            if (count($errors) > 0) {
                $this->logger->error(__METHOD__ . ' Error while validating Corvet data', [
                    'vin' => $vin, 'errors' => $errors]);
            }

            // update DTO object with data incoming from Corvet
            $vehicleData = $this->mapCorvetDataToSystemVehicleData($data, $vehicleData);
        }
        
        return $vehicleData;
    }

    private function lookUpLCDVLabels(SystemVehicleData $vehicleData): SystemVehicleData
    {
        if(empty($vehicleData->getLcdv())) {
            $this->logger->error(__METHOD__ . ' LCDV code is empty for vehicle with VIN: '.$vehicleData->getVin());
            return $vehicleData;
        }

        $label = $this->vehicleLabelService->findLabelByLongestMatchingLcdv($vehicleData->getLcdv());
        if (null === $label) {
            $this->logger->warning(__METHOD__ .' No label found for LCDV code: '.$vehicleData->getLcdv());
            return $vehicleData;
        }        
        $this->logger->debug(__METHOD__ . ' Label found for LCDV code: '.$vehicleData->getLcdv(), ['label' => $label]);

        $vehicleData->setLabel($label);
        return $vehicleData;
    }

    private function calculateImageUrl(string $lcdv, string $brand, array $vehicleOptions = []): ?string
    {
        $visualResponse = $this->visual3DManager->loadImages($lcdv, $brand, 'WEB', $vehicleOptions); 

        $imageUrl = $visualResponse['data'] ?? null;
        return $imageUrl;
    }
    
    public function getV1VehiclesConstraints(): Assert\Collection
    {
        return new Assert\Collection([
            'fields' => [
                'success' => new Assert\All([
                    'constraints' => [
                        new Assert\Collection([
                            'fields' => [
                                'veh' => new Assert\Collection([
                                    'fields' => [
                                        'OID' => new Assert\Required([
                                            new Assert\NotBlank()
                                        ]),
                                        'VEHICULE' => new Assert\Collection([
                                            'fields' => [
                                                'VEH_VIN' => new Assert\Required([
                                                    new Assert\NotBlank()
                                                ]),
                                            ],
                                            'allowExtraFields' => true,
                                        ]),
                                    ],
                                    'allowExtraFields' => true,
                                ]),
                            ],
                            'allowExtraFields' => true,
                        ]),
                    ],
                ]),
            ],
            'allowExtraFields' => true,
        ]);
    }

    private function getV1ExtractElementsConstraints(): Assert\Collection
    {
        $vehiculeConstraints = new Assert\Collection([
            'fields' => [
                'VEH_VIN' => new Assert\NotBlank(),
            ],
            'allowExtraFields' => true,
        ]);

        $vehConstraints = new Assert\Collection([
            'fields' => [
                'VEHICULE' => $vehiculeConstraints,
            ],
            'allowExtraFields' => true,
        ]);

        $vehiclesConstraints = new Assert\All([
            'constraints' => new Assert\Collection([
                'fields' => [
                    'veh' => $vehConstraints,
                ],
                'allowExtraFields' => true,
            ]),
        ]);

        $profileConstraints = new Assert\Collection([
            'fields' => [
                'id' => new Assert\NotBlank(),
            ],
            'allowExtraFields' => true,
        ]);

        $constraints = new Assert\Collection([
            'fields' => [
                'success' => new Assert\Collection([
                    'fields' => [
                        'profile' => $profileConstraints,
                        'vehicles' => $vehiclesConstraints,
                    ],
                    'allowExtraFields' => true,
                ]),
            ],
            'allowExtraFields' => true            
        ]);
        
        return $constraints;
    }

    private function getV1CatTicketConstraints(): Assert\Collection
    {
        $constraints = new Assert\Collection([
            'fields' => [
                'success' => new Assert\Collection([
                    'fields' => [
                        'ticket' => [
                            new Assert\NotBlank(),
                            new Assert\Type([
                                'type' => 'string',
                                'message' => 'The ticket should be a string',
                            ]),
                        ],
                    ],
                    'allowExtraFields' => true,
                ]),
            ],
            'allowExtraFields' => true,
        ]);

        return $constraints;
    }

    private function getV1CorvetVinData(): Assert\Collection
    {
        // Vin should be a not empty string
        $constraints = new Assert\Collection([
            'fields' => [
                'success' => new Assert\Collection([
                    'fields' => [
                        'VEHICULE' => new Assert\Collection([
                            'fields' => [
                                'DONNEES_VEHICULE' => new Assert\Collection([
                                    'fields' => [
                                        'VIN' => new Assert\NotBlank(),
                                    ],
                                    'allowExtraFields' => true,
                                ]),
                            ],
                            'allowExtraFields' => true,
                        ]),
                    ],
                    'allowExtraFields' => true,
                ]),
            ],
            'allowExtraFields' => true,
        ]);
        
        return $constraints;
    }

    /**
     * Map a veh element retrieved from the v1/vehicles endpoint to a SystemVehicleData object
     * {
     *      "success": [
     *          {
     *              {
     *                "veh": {
     *                   "REL_VEH_TYPE": "USER",
     *                   "REL_VEH_RELEASE": "2024-10-11T14:23:41",
     *                   "OID": "BUCT200009366479",
     *                   "VEHICULE": {
     *                       "VEH_CLASS_LABEL": "308",
     *                       "VEH_LAST_KNOWN_OD_REPORT": "23444",
     *                       "VEH_LCDV": "1PP5A5ZNKAB0A0B0M09VDBFX",
     *                       "VEH_TRADEMARK": "308",
     *                       "VEH_VERSION_LABEL": "308",
     *                       "VEH_VIN": "vr3attently036189",
     *                       "VEH_VIS": "LY036189"
     *                   }
     *               },
     *               "reldate": "2024-10-11T14:23:41"
     *           },
     *       ]
     *   }
     * 
     * 
     * MilkyWay                    		MongoDB                     	C@/Valorization
     * id                          			id                          generated by space middelware uuid
     * vin                         			vin                         vin
     * sdp                         			sdp                         		"CVMP" : constant value for all vehicles retrived by C@
     * lcdv                        			versionId                   	corvet LCDV
     *** type                        			type                        		corvet based on attribute DXD
     * 												$vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
     * 												$vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
     * modelDescription(name)      	label                       		Calculate label based on LCDV
     * regTimeStamp                		regTimeStamp                	user_data => reldate
     * enrollmentStatus            		enrollmentStatus            	null
     * connectorType               		connectorType               	null (to be check)
     * make                        		make                        	user_data => VEH_TRADEMARK
     * subMake                     		subMake                     	user_data => VEH_VERSION_LABEL
     * market                      		market                      	null
     * brand                       		brand                       	brand
     * lastUpdate                  		lastUpdate                  	NOW() (calculated)
     * year                        			year                        		null
     * warrantyStartDate           	warrantyStartDate           user_data => VEH_WARRANTY_START_DATE
     * @return array<SystemVehicleData>
     */
    public static function mapV1VehiclesToSystemVehicleData(array $mockedData, array $options = []): array
    {
        $systemVehicleDataList = [];

        foreach ($mockedData['success'] as $vehicleData) {
            $systemVehicleData = new SystemVehicleData();
            $vin = $vehicleData['veh']['VEHICULE']['VEH_VIN'];
            $systemVehicleData->setVin($vin)
                ->setUserId($options['userId'])
                ->setVin($vin)
                ->setSdp(self::SDP_CVMP)
                ->setLcdv($vehicleData['veh']['VEHICULE']['VEH_LCDV'] ?? null)
                ->setRegTimestamp($vehicleData['relDate'] ?? null)
                ->setEnrollmentStatus(null)
                ->setConnectorType(null)
                ->setMake($vehicleData['veh']['VEHICULE']['VEH_TRADEMARK'] ?? null)
                ->setSubMake($vehicleData['veh']['VEHICULE']['VEH_VERSION_LABEL'] ?? null)
                ->setMarket(null)
                ->setBrandCode($options['brand'] ?? null)
                ->setLastUpdate(strval(time()))
                ->setWarrantyStartDate($vehicleData['veh']['VEHICULE']['VEH_WARRANTY_START_DATE'] ?? null);

            $systemVehicleDataList[$vin] = $systemVehicleData;
        }

        return $systemVehicleDataList;
    }
   
    private function mapCorvetDataToSystemVehicleData(array $data, SystemVehicleData $vehicleData): SystemVehicleData
    {
        // may be already set by the previous call
        $corvetLcdv =$data['success']['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'] ?? null;
        if(!empty($corvetLcdv)) {
            $vehicleData->setLcdv($corvetLcdv);
        }
        $vehicleData->setCorvetAttributes($data['success']['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? []);

        $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $vehicleData->getCorvetAttributes());
        $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
        $vehicleData->setType($vehicleType);

        return $vehicleData;
    }
}