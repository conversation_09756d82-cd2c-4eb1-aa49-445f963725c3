<?php

namespace App\Tests\Manager;

use App\Event\SpaceVehicleUpdatedEvent;
use App\Helper\BrandHelper;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\CatalogManager;
use App\Manager\SubscriptionManager;
use App\Manager\VehicleManager;
use App\Manager\Visual3DManager;
use App\Model\VehicleModel;
use App\Model\VehicleOrderModel;
use App\Service\CorvetService;
use App\Service\FeatureCodeService;
use App\Service\MongoAtlasQueryService;
use App\Service\SystemSdprClient;
use App\Service\SystemUserDataClient;
use App\Service\UserDataService;
use App\Service\VehicleLabelService;
use App\Service\VehicleService;
use App\Service\XFVehicleRefreshService;
use App\Service\XPVehicleRefreshService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleManagerTest extends TestCase
{
    private VehicleManager $vehicleManager;

    private VehicleService $vehicleService;
    private SerializerInterface $serializer;
    private DenormalizerInterface $denormalizer;
    private ValidatorInterface $validator;
    private MongoAtlasQueryService $mongoService;
    private LoggerInterface $logger;
    private EventDispatcherInterface $dispatcher;
    private VehicleLabelService $vehicleLabelService;
    private NormalizerInterface $normalizer;
    private CatalogManager $catalogManager;
    private SubscriptionManager $subscriptionManager;

    public function setUp(): void
    {
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->denormalizer = $this->createMock(DenormalizerInterface::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->mongoService = $this->createMock(MongoAtlasQueryService::class);
        $this->vehicleService = $this->createMock(VehicleService::class);
        $this->dispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->catalogManager = $this->createMock(CatalogManager::class);
        $this->subscriptionManager = $this->createMock(SubscriptionManager::class);

        $this->vehicleManager = new VehicleManager(
            $this->vehicleService,
            $this->serializer,
            $this->denormalizer,
            $this->validator,
            $this->mongoService,
            $this->dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $this->vehicleLabelService,
            $this->normalizer,
            $this->catalogManager,
            $this->subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );

        $this->logger = $this->createMock(LoggerInterface::class);
        $this->vehicleManager->setLogger($this->logger);
    }

    public function testVehicleValuesValidate(): void
    {
        $vehicleModel = new VehicleModel();
        $vehicleModel->setBrand('AP');
        $vehicleModel->setVehicleOrder(new VehicleOrderModel());
        $response = $this->vehicleManager->vehicleValuesValidate($vehicleModel);
        $this->assertIsArray($response);
    }

    public function testVehicleValuesValidateWithErrorsMessages(): void
    {
        $this->mockVehicleValuesValidate([
            $this->createError('Error 1'),
            $this->createError('Error 2'),
        ]);

        $vehicleModel = new VehicleModel();
        $vehicleModel->setBrand('AP');
        $vehicleModel->setVehicleOrder(new VehicleOrderModel());
        $response = $this->vehicleManager->vehicleValuesValidate($vehicleModel);
        $this->assertIsArray($response);
        $this->assertCount(4, $response);
    }

    public function testGetVehicleModel(): void
    {
        $this->mockGetVehicleModel(new VehicleModel());
        $content = ['brand' => 'OP', 'visual' => '', 'culture' => 'fr_FR', 'versionId' => '1GJOA5UMDKBDA0B0M09VD6FX'];

        $response = $this->vehicleManager->getVehicleModel($content);
        $this->assertInstanceOf(VehicleModel::class, $response);
    }

    public function testGetVehiclesWithEmptyDBOrders(): void
    {
        $service = $this->createMock(VehicleService::class);
        $service
            ->expects($this->once())
            ->method('getVehiclesOnOrder')
            ->willReturn(new WSResponse(200, ''));

        $serializer = $this->createMock(SerializerInterface::class);
        $denormalizer = $this->createMock(DenormalizerInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $mongoService = $this->createMock(MongoAtlasQueryService::class);
        $dispatcher = $this->createMock(EventDispatcherInterface::class);
        $vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $normalizer = $this->createMock(NormalizerInterface::class);
        $catalogManager = $this->createMock(CatalogManager::class);
        $subscriptionManager = $this->createMock(SubscriptionManager::class);

        $vehicleManager = new VehicleManager(
            $service,
            $serializer,
            $denormalizer,
            $validator,
            $mongoService,
            $dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        $actualResponse = $vehicleManager->getVehicles('123');

        $this->assertSame($actualResponse->getData(), ['vehicles' => []]);
        $this->assertSame($actualResponse->getCode(), 200);
    }

    public function testGetVehicleSummaryWithSuccess(): void
    {
        // mock getVehicleInfo response
        $data = json_encode([
            'documents' => [['vehicle' => ['vehicleInfo']]],
        ]);
        $mongoResponse = new WSResponse(Response::HTTP_OK, $data);
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($mongoResponse);

        $vehicleModel = $this->createVehicleModelFixture();

        $this->denormalizer
            ->expects($this->once())
            ->method('denormalize')
            ->willReturn($vehicleModel);

        // mock service response
        $wsSuccessResponse = new WSResponse(Response::HTTP_OK, ['success' => ['pdf_url' => 'pdf_url']]);
        $this->vehicleService
            ->expects($this->once())
            ->method('getOrderSummary')
            ->willReturn($wsSuccessResponse);

        $vehicleId = '222';
        $userId = '444';
        $response = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $arrResponse = $response->toArray();
        $code = $arrResponse['code'] ?? null;
        $data = $arrResponse['content']['success'] ?? [];

        $this->assertSame(Response::HTTP_OK, $code);
        $this->assertArrayHasKey('url', $data);
        $this->assertSame('pdf_url', $data['url']);
    }

    public function testGetVehicleSummaryWithError(): void
    {
        // mock getVehicleInfo response
        $data = json_encode([
            'documents' => [['vehicle' => ['vehicleInfo']]],
        ]);
        $mongoResponse = new WSResponse(Response::HTTP_OK, $data);
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($mongoResponse);

        $vehicleModel = $this->createVehicleModelFixture();

        $this->denormalizer
            ->expects($this->once())
            ->method('denormalize')
            ->willReturn($vehicleModel);

        // mock service response
        $wsResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, ['success' => ['pdf_url' => 'pdf_url']]);
        $this->vehicleService
            ->expects($this->once())
            ->method('getOrderSummary')
            ->willReturn($wsResponse);

        $this->logger
            ->expects($this->once())
            ->method('error');

        $vehicleId = '222';
        $userId = '444';
        $response = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $arrResponse = $response->toArray();

        $code = $arrResponse['code'] ?? null;
        $message = $arrResponse['content']['error']['message'] ?? null;
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $code);
        $this->assertSame('server error', $message);
    }

    public function testGetVehicleSummaryWillThrowManagedException(): void
    {
        // mock getVehicleInfo response..that throw internal exception
        $data = json_encode([
            'documents' => [['vehicle' => null]],
        ]);
        $mongoResponse = new WSResponse(Response::HTTP_OK, $data);
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($mongoResponse);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::getVehicleSummary Catched Exception'));

        $vehicleId = '222';
        $userId = '444';
        $response = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $arrResponse = $response->toArray();

        $code = $arrResponse['code'] ?? null;
        $message = $arrResponse['content']['error']['message'] ?? null;
        $this->assertSame(Response::HTTP_NOT_FOUND, $code);
        $this->assertSame('vehicle not found', $message);
    }

    private function createVehicleModelFixture(): VehicleModel
    {
        $vehicleOrder = new VehicleOrderModel();
        $vehicleOrder->setOrderFormId('123')
            ->setMopId('MOP333');

        $vehicleModel = new VehicleModel();
        $vehicleModel->setBrand('Peugeot')
            ->setVehicleOrder($vehicleOrder)
            ->setCountry('FR');

        return $vehicleModel;
    }

    public function testGetVehiclesException(): void
    {
        $logger = $this->CreateMock(LoggerInterface::class);
        $service = $this->createMock(VehicleService::class);
        $service
            ->expects($this->once())
            ->method('getVehiclesOnOrder')
            ->willThrowException(new \Exception());
        $service->setLogger($logger);
        $serializer = $this->createMock(SerializerInterface::class);
        $denormalizer = $this->createMock(DenormalizerInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $mongoService = $this->createMock(MongoAtlasQueryService::class);
        $dispatcher = $this->createMock(EventDispatcherInterface::class);
        $vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $normalizer = $this->createMock(NormalizerInterface::class);
        $catalogManager = $this->createMock(CatalogManager::class);
        $subscriptionManager = $this->createMock(SubscriptionManager::class);

        $vehicleManager = new VehicleManager(
            $service,
            $serializer,
            $denormalizer,
            $validator,
            $mongoService,
            $dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        $vehicleManager->setLogger($logger);
        $actualResponse = $vehicleManager->getVehicles('123');

        $this->assertInstanceOf(ErrorResponse::class, $actualResponse);
    }

    private function mockGetVehicleModel(VehicleModel $vehicleModel): void
    {
        $this->serializer
            ->expects($this->once())
            ->method('deserialize')
            ->willReturn($vehicleModel);
    }

    private function mockVehicleValuesValidate(array $messages = []): void
    {
        $this->validator
            ->expects($this->exactly(2))
            ->method('validate')
            ->willReturn(new ConstraintViolationList($messages));
    }

    public function testCreateOrUpdateVehicleReturnSuccess(): void
    {
        $userId = '123';

        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleModel($vehicleModel);

        $this->mockVehicleValuesValidate([]);

        $wsResponse = new WSResponse(Response::HTTP_OK, 'Success response');
        $this->mockServiceMethodCreateOrUpdateVehicle($userId, $vehicleModel, $wsResponse);

        $this->dispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(SpaceVehicleUpdatedEvent::class));

        $content = [
            'userId' => $userId,
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'culture' => 'fr_FR',
            'versionId' => 'version-id-test',
        ];

        $result = $this->vehicleManager->createOrUpdateVehicle($content);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $data = $result->getData();
        $this->assertSame('MOP-123', $data['mopId'] ?? '');
        $this->assertSame('Vehicle data has been saved successfully', $data['message'] ?? '');
    }

    public function testCreateOrUpdateVehicleReturnValidationErrors(): void
    {
        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleModel($vehicleModel);

        $this->mockMethodVehicleValuesValidate([]);

        $content = [
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'culture' => 'fr_FR',
            'brand' => 'OP',
            'versionId' => 'version-id-test',
        ];
        $result = $this->vehicleManager->createOrUpdateVehicle($content);
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $arrResult = $result->toArray();
        $this->assertSame(Response::HTTP_BAD_REQUEST, $arrResult['code'] ?? 0);
        $message = $arrResult['content']['error']['message'] ?? '';
        $this->assertStringContainsString('The user ID value should not be blank.', $message);
    }

    public function testCreateOrUpdateVehicleReturnErrorOnSaving(): void
    {
        $userId = '123';

        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleModel($vehicleModel);

        $this->mockMethodVehicleValuesValidate([]);

        $wsResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, 'Error response');
        $this->mockServiceMethodCreateOrUpdateVehicle($userId, $vehicleModel, $wsResponse);

        $content = [
            'userId' => $userId,
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'brand' => 'OP',
            'culture' => 'fr_FR',
            'versionId' => 'version-id-test',
        ];
        $result = $this->vehicleManager->createOrUpdateVehicle($content);
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $arrResult = $result->toArray();
        $this->assertSame(Response::HTTP_BAD_REQUEST, $arrResult['code'] ?? 0);
        $message = $arrResult['content']['error']['message'] ?? '';
        $this->assertStringContainsString('An error has occurred, vehicle data has not been saved', $message);
    }

    public function testCreateOrUpdateVehicleWillThrowExceptionAndReturnError(): void
    {
        $userId = '123';
        $this->mockMethodGetVehicleModelWillThrowException(new \Exception('Exception message', 123));

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::createOrUpdateVehicle Catched Exception'));

        $content = [
            'userId' => $userId,
            'visual' => 'FIAT',
            'mopId' => 'MOP-123',
            'brand' => 'OP',
            'culture' => 'fr_FR',
            'versionId' => 'version-id-test',
        ];

        $result = $this->vehicleManager->createOrUpdateVehicle($content);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $arrResult = $result->toArray();
        $this->assertSame(400, $arrResult['code'] ?? 0);
        $message = $arrResult['content']['error']['message'] ?? '';
        $this->assertStringContainsString('Exception message', $message);
    }

    private function mockMethodGetVehicleModel(VehicleModel $vehicleModel): void
    {
        $this->serializer
            ->expects($this->once())
            ->method('deserialize')
            ->willReturn($vehicleModel);
    }

    private function mockMethodGetVehicleModelWillThrowException(\Throwable $e): void
    {
        $this->serializer
            ->expects($this->once())
            ->method('deserialize')
            ->willThrowException($e);
    }

    private function mockMethodVehicleValuesValidate(array $messages = []): void
    {
        $this->validator
            ->expects($this->exactly(2))
            ->method('validate')
            ->willReturn(new ConstraintViolationList($messages));
    }

    private function mockServiceMethodCreateOrUpdateVehicle(
        string $userId,
        VehicleModel $vehicleModel,
        WSResponse $response
    ): void {
        $this->vehicleService
            ->expects($this->once())
            ->method('createOrUpdateVehicle')
            ->with(
                $userId,
                $vehicleModel
            )
            ->willReturn($response);
    }

    private function getVehicleModel(): VehicleModel
    {
        $vehicleModel = new VehicleModel();
        $vehicleModel->setId('678');
        $vehicleModel->setBrand('AP');
        $vehicleModel->setVisual('AP-test');
        $vehicleModel->setLabel('Label Test');
        $vehicleModel->setVersionId('1.2.3');

        $vehicleOrder = new VehicleOrderModel();
        $vehicleOrder->setMopId('MOP-123');
        $vehicleOrder->setOrderFormId('556788');

        $vehicleModel->setVehicleOrder($vehicleOrder);

        return $vehicleModel;
    }

    private function getVehicleModelToArray(): array
    {
        $vehicle = [
            'id' => '678',
            'brand' => 'AP',
            'visual' => 'AP-test',
            'label' => 'Label Test',
            'versionId' => '1.2.3',
            'vehicleOrder' => [
                'mopId' => 'MOP-123',
                'orderFormId' => '556788',
            ],
        ];

        return $vehicle;
    }

    public function testMarkOrdersAsRead(): void
    {
        $logger = $this->CreateMock(LoggerInterface::class);
        $service = $this->createMock(VehicleService::class);
        $service->expects($this->any())
            ->method('setOrderIsUpdated')
            ->willReturn(new WSResponse(200, 'updated OK'));
        $service->setLogger($logger);
        $serializer = $this->createMock(SerializerInterface::class);
        $denormalizer = $this->createMock(DenormalizerInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);
        $mongoService = $this->createMock(MongoAtlasQueryService::class);
        $dispatcher = $this->createMock(EventDispatcherInterface::class);
        $vehicleLabelService = $this->createMock(VehicleLabelService::class);
        $normalizer = $this->createMock(NormalizerInterface::class);
        $catalogManager = $this->createMock(CatalogManager::class);
        $subscriptionManager = $this->createMock(SubscriptionManager::class);

        $vehicleManager = new VehicleManager(
            $service,
            $serializer,
            $denormalizer,
            $validator,
            $mongoService,
            $dispatcher,
            $this->createMock(XPVehicleRefreshService::class),
            $this->createMock(XFVehicleRefreshService::class),
            $this->createMock(UserDataService::class),
            $this->createMock(CorvetService::class),
            $this->createMock(SystemUserDataClient::class),
            $this->createMock(Visual3DManager::class),
            $this->createMock(BrandHelper::class),
            $this->createMock(SystemSdprClient::class),
            $this->createMock(FeatureCodeService::class),
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $this->createMock(\App\Service\SystemUserDBService::class),
            $this->createMock(\App\Manager\SystemUserDBManager::class)
        );
        $vehicleManager->setLogger($logger);
        $response = $vehicleManager->markOrdersAsRead('123', ['v1', 'v2']);
        $this->assertEquals($response, null);
    }

    public function testGetVehicleSummaryReturnSuccessResponse(): void
    {
        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleInfo($vehicleModel);
        $wsResponse = new WSResponse(Response::HTTP_OK, ['success' => ['pdf_url' => 'pdf url']]);
        $this->mockServiceMethodGetOrderSummary($wsResponse);

        $userId = '123';
        $vehicleId = 'MOP-123';
        $result = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertSame(Response::HTTP_OK, $result->getCode());
        $this->assertSame('pdf url', $result->getData()['url'] ?? '');
    }

    public function testGetVehicleSummaryReturnErrorResponse(): void
    {
        $vehicleModel = $this->getVehicleModel();
        $this->mockMethodGetVehicleInfo($vehicleModel);
        $wsResponse = new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, ['error' => ['message' => 'Error message']]);
        $this->mockServiceMethodGetOrderSummary($wsResponse);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::getVehicleSummary Error response'));

        $userId = '123';
        $vehicleId = 'MOP-123';
        $result = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $data = $result->toArray();
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $data['code'] ?? 0);
        $this->assertSame('Error message', $data['content']['error']['message'] ?? '');
    }

    public function testGetVehicleSummaryThrowExceptionAndReturnErrorResponse(): void
    {
        $this->mockMethodGetVehicleInfoWillThrowException(new \Exception('Exception message', Response::HTTP_INTERNAL_SERVER_ERROR));

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('VehicleManager::getVehicleSummary Catched Exception'));

        $userId = '123';
        $vehicleId = 'MOP-123';
        $result = $this->vehicleManager->getVehicleSummary($vehicleId, $userId);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $data = $result->toArray();
        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $data['code'] ?? 0);
        $this->assertSame('Exception message', $data['content']['error']['message'] ?? '');
    }

    public function testManageOVData(): void
    {
        $versionId = '1GJOA5UMDKBDA0B0M09VD6FX';
        $vin = 'test-vin';
        $vehicle = [
            'versionId' => $versionId,
            'vin' => $vin,
            'brand' => 'OP',
            'country' => 'FR',
        ];
        $attributes['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] = ['DZJ20CD', 'DZVCBCD', 'DZZ0VCD'];
        $vehicleExpected = [
            'versionId' => $versionId,
            'vin' => $vin,
            'brand' => 'VX',
            'country' => 'GB',
            'language' => 'en',
        ];

        $this->vehicleService->expects($this->once())
        ->method('isOVVehicle')
        ->willReturn(true);

        $this->vehicleService->expects($this->once())
        ->method('getVehicleBrand')
        ->willReturn('VX');

        $response = $this->vehicleManager->manageOVData($vehicle);

        $this->assertSame($vehicleExpected, $response);
        $this->assertIsArray($response);
    }

    private function mockMethodGetVehicleInfoWillThrowException(\Exception $e): void
    {
        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willThrowException($e);
    }

    private function mockMethodGetVehicleInfo(VehicleModel $vehicleModel): void
    {
        $data = json_encode([
            'documents' => [
                ['vehicle' => $this->getVehicleModelToArray()],
            ],
        ]);
        $response = new WSResponse(Response::HTTP_OK, $data);

        $this->mongoService
            ->expects($this->once())
            ->method('aggregate')
            ->willReturn($response);

        $this->denormalizer
            ->expects($this->once())
            ->method('denormalize')
            ->willReturn($vehicleModel);
    }

    private function mockServiceMethodGetOrderSummary(WSResponse $wsResponse): void
    {
        $this->vehicleService
            ->expects($this->once())
            ->method('getOrderSummary')
            ->willReturn($wsResponse);
    }

    private function createError($message)
    {
        $errorMock = $this->createMock(ConstraintViolationInterface::class);
        $errorMock->expects($this->any())
            ->method('getMessage')
            ->willReturn($message);

        return $errorMock;
    }

    public function testGetVehicleDetailWithMockedResponse(): void
    {
        // Skip this test for now as it requires more complex mocking
        $this->markTestSkipped('This test requires more complex mocking of the VehicleManager::getVehicleDetail method');

        $userId = 'testUserId';
        $critariaValue = 'testVin';
        $critariaKey = 'vin';
        $language = 'en';
        $country = 'FR';

        $mockedVehicleData = [
            "documents" => [
                [
                    'vehicle' => [
                        [
                            "modelDescription" => "Peugeot 3008",
                            "shortLabel" => "3008",
                            "regTimeStamp" => 1609459200, // January 1, 2021
                            "brand" => "Peugeot",
                            "country" => "FR",
                            "versionId" => "1.2 PureTech",
                            "year" => "2021",
                            "type" => "SUV",
                            "mileage" => [
                                "value" => 15000,
                                "date" => 1612137600 // February 1, 2021
                            ],
                            "vin" => "VF3ATTENTGY182416",
                            "picture" => "https://example.com/picture.jpg",
                            "sdp" => "SDP1",
                            "isOrder" => true,
                            "featureCode" => [
                                [
                                    "code" => "FC1",
                                    "status" => "active",
                                    "value" => "value1",
                                    "config" => [
                                        "type" => "type1"
                                    ]
                                ],
                                [
                                    "code" => "FC2",
                                    "status" => "inactive",
                                    "value" => "value2",
                                    "config" => [
                                        "type" => "type2"
                                    ]
                                ]
                            ],
                            "addStatus" => "added",
                            "lastUpdate" => "2021-02-01T12:00:00Z",
                            "make" => "Peugeot",
                            "market" => "Europe",
                            "warrantyStartDate" => "2021-01-01T00:00:00Z",
                            "isO2x" => true
                        ]
                    ]
            ]
        ]];

        $mockedResponse = new WSResponse(
            Response::HTTP_OK,
            json_encode($mockedVehicleData)
        );

        $this->mongoService->expects($this->any())
            ->method('find')
            ->willReturn($mockedResponse);

        $response = $this->vehicleManager->getVehicleDetail($userId, $critariaValue, $language, $country, $critariaKey);

        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());

        $expectedVehicleXPFormat = [
            'vehicleInfo' => [
                'vin' => 'VF3ATTENTGY182416',
                'lcdv' => null,
                'visual' => 'https://example.com/picture.jpg',
                'short_label' => '3008',
                'warranty_start_date' => '2021-01-01T00:00:00Z',
                'attributes' => [],
                'type_vehicle' => 'SUV',
                'mileage' => [
                    'value' => 15000,
                    'date' => 1612137600
                ],
            ],
            'eligibility' => [],
            'vehicleProducts' => [
                'productsCatalog' => [],
                'purchasedProducts' => [],
                'productGroupNameStatus' => []
            ],
            'settingsUpdate' => '2021-02-01T12:00:00Z'
        ];

        $this->assertEquals($expectedVehicleXPFormat, $response->getData());
   }

   public function testRemoveNullValues()
    {
        $input = [
            'key1' => 'value1',
            'key2' => null,
            'key3' => 'value3',
            'key4' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey2' => null,
                'nestedKey3' => 'nestedValue3',
            ],
            'key5' => null,
        ];

        $expectedOutput = [
            'key1' => 'value1',
            'key3' => 'value3',
            'key4' => [
                'nestedKey1' => 'nestedValue1',
                'nestedKey3' => 'nestedValue3',
            ],
        ];

        $result = $this->vehicleManager->removeNullValues($input);

        $this->assertEquals($expectedOutput, $result);
    }

    public function testRemoveNullValuesARealCase(): void
    {
        $jsonContent = '{"success":[{"vin":"VIN123456789012345","lcdv":null,"visual":"http:\/\/example.com\/picture1.jpg","short_label":"My Car","warranty_start_date":null,"command":null,"sdp":"SDP1"},{"vin":"VIN987654321098765","lcdv":"LCDV2","visual":"http:\/\/example.com\/picture2.jpg","short_label":"Family Car","warranty_start_date":1234567890,"command":"command2","sdp":"SDP2"}]}';
        $input = json_decode($jsonContent, true);

        $expectedOutput = [
            'success' => [
                [
                    'vin' => 'VIN123456789012345',
                    'visual' => 'http://example.com/picture1.jpg',
                    'short_label' => 'My Car',
                    'sdp' => 'SDP1',
                ],
                [
                    'vin' => 'VIN987654321098765',
                    'lcdv' => 'LCDV2',
                    'visual' => 'http://example.com/picture2.jpg',
                    'short_label' => 'Family Car',
                    'warranty_start_date' => 1234567890,
                    'command' => 'command2',
                    'sdp' => 'SDP2',
                ],
            ],
        ];

        $result = $this->vehicleManager->removeNullValues($input);

        $this->assertEquals($expectedOutput, $result);
    }
}
