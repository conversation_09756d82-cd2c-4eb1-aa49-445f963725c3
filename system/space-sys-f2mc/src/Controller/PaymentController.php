<?php

namespace App\Controller;

use App\Manager\PaymentManager;
use App\Trait\ValidationResponseTrait;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('v1', name: 'payment_')]
class PaymentController extends AbstractController
{
    use ValidationResponseTrait;

    private PaymentManager $paymentManager;

    public function __construct(PaymentManager $paymentManager)
    {
        $this->paymentManager = $paymentManager;
    }

    #[OA\Tag(name: 'Payment')]
    #[OA\Parameter(
        name: 'accesstoken',
        in: 'header',
        description: 'Access token',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'message', type: 'string'),
                    ]
                ),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Operation failed',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]

    #[Route('/user/payment-method-url', name: 'new_url', methods: ['GET'])]
    public function addUrl(Request $request): JsonResponse
    {
        $headers = $request->headers->all();

        $accessToken = $headers['accesstoken'][0] ?? '';
        $response = $this->paymentManager->addUrl($accessToken)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[OA\Tag(name: 'Payment')]
    #[OA\Parameter(
        name: 'accesstoken',
        in: 'header',
        description: 'Access token',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'success',
                    type: 'array',
                    items: new OA\Items(
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'string'),
                            new OA\Property(property: 'type', type: 'string', nullable: true),
                            new OA\Property(property: 'transactionTypeId', type: 'integer'),
                            new OA\Property(
                                property: 'transactionType',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'name', type: 'string'),
                                ]
                            ),
                            new OA\Property(property: 'userId', type: 'string'),
                            new OA\Property(property: 'amount', type: 'number'),
                            new OA\Property(property: 'invoiceId', type: 'string', nullable: true),
                            new OA\Property(property: 'walletTransactionId', type: 'string', nullable: true),
                            new OA\Property(property: 'offerTransactionId', type: 'string', nullable: true),
                            new OA\Property(property: 'transactionDate', type: 'string', format: 'date-time'),
                            new OA\Property(property: 'walletTransaction', type: 'string', nullable: true),
                            new OA\Property(property: 'invoice', type: 'string', nullable: true),
                            new OA\Property(property: 'offerTransaction', type: 'string', nullable: true),
                            new OA\Property(
                                property: 'externalTransaction',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'string'),
                                    new OA\Property(property: 'transactionTypeId', type: 'integer'),
                                    new OA\Property(property: 'amount', type: 'number'),
                                    new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
                                    new OA\Property(property: 'chargingSessionId', type: 'string'),
                                    new OA\Property(
                                        property: 'chargingSession',
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'id', type: 'string'),
                                            new OA\Property(property: 'locationId', type: 'string'),
                                            new OA\Property(
                                                property: 'location',
                                                type: 'object',
                                                properties: [
                                                    new OA\Property(property: 'id', type: 'string'),
                                                    new OA\Property(property: 'name', type: 'string'),
                                                    new OA\Property(
                                                        property: 'addressDetail',
                                                        type: 'object',
                                                        properties: [
                                                            new OA\Property(property: 'address', type: 'string'),
                                                            new OA\Property(property: 'city', type: 'string'),
                                                            new OA\Property(property: 'country', type: 'string'),
                                                            new OA\Property(property: 'postalCode', type: 'string'),
                                                            new OA\Property(property: 'province', type: 'string', nullable: true),
                                                            new OA\Property(property: 'state', type: 'string'),
                                                        ]
                                                    ),
                                                    new OA\Property(property: 'avgRating', type: 'number'),
                                                    new OA\Property(property: 'cpo', type: 'string'),
                                                    new OA\Property(property: 'cpoId', type: 'string'),
                                                    new OA\Property(property: 'isPaid', type: 'boolean'),
                                                    new OA\Property(property: 'isPromoted', type: 'boolean'),
                                                    new OA\Property(property: 'locationRefId', type: 'string'),
                                                    new OA\Property(property: 'openingHours', type: 'string', nullable: true),
                                                    new OA\Property(property: 'operator', type: 'string'),
                                                    new OA\Property(property: 'power', type: 'number'),
                                                    new OA\Property(property: 'status', type: 'string'),
                                                ]
                                            ),
                                            new OA\Property(property: 'chargingCompletedAt', type: 'string', format: 'date-time'),
                                            new OA\Property(property: 'commissionPercentage', type: 'string'),
                                            new OA\Property(property: 'cost', type: 'string'),
                                            new OA\Property(property: 'cpoId', type: 'string'),
                                            new OA\Property(property: 'energy', type: 'string'),
                                            new OA\Property(property: 'evseId', type: 'string'),
                                            new OA\Property(property: 'fixedCommission', type: 'string'),
                                            new OA\Property(property: 'power', type: 'number'),
                                            new OA\Property(property: 'powerType', type: 'string'),
                                            new OA\Property(property: 'startedAt', type: 'string', format: 'date-time'),
                                            new OA\Property(property: 'status', type: 'string'),
                                            new OA\Property(property: 'vehicleMake', type: 'string'),
                                            new OA\Property(property: 'vehicleModel', type: 'string'),
                                            new OA\Property(property: 'currency', type: 'string'),
                                        ]
                                    )
                                ]
                            )
                        ]
                    )
                )
            ]
        )
    )]

    #[OA\Response(
        response: 400,
        description: 'Operation failed',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Unauthorized',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                ]),
            ]
        )
    )]
    #[Route('/user/transactions', name: 'payment_transactions', methods: ['GET'])]
    public function getPaymentHistory(Request $request): JsonResponse
    {
        $headers = $request->headers->all();
        $accessToken = $headers['accesstoken'][0] ?? '';
        $response = $this->paymentManager->getPaymentHistory($accessToken)->toArray();

        return $this->json($response['content'], $response['code']);
    }

}
