<?php

use App\Controller\ChargingSessionController;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Manager\ChargingSessionManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingSessionControllerTest extends WebTestCase
{
    private $chargingSessionController;
    private $ChargingSessionManager;

    protected function setUp(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $this->ChargingSessionManager = $this->createMock(ChargingSessionManager::class);
        $this->chargingSessionController = new ChargingSessionController($validator);
        $this->chargingSessionController->setContainer(static::getContainer());
    }

    public function testStartReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging-sessions/start', Request::METHOD_POST, [], [], [], $headers, $data);
        $this->ChargingSessionManager->method('validateChargingSessionData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->ChargingSessionManager->method('startSession')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));

        $response = $this->chargingSessionController->startSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid value', $response->getContent());
    }

    public function testStartReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['valid_param' => 'value']);
        $request = Request::create('/charging-sessions/start', Request::METHOD_POST, [], [], [], $headers, $data);

        $this->ChargingSessionManager->method('validateChargingSessionData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => "idSession"
        ];

        $this->ChargingSessionManager->method('startSession')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingSessionController->startSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":"idSession"}}', $response->getContent());
    }

    public function testStartReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/charging-sessions/start', Request::METHOD_POST, [], [], [], $headers, $data);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['data'],
            null,
            'data',
            null
        );

        $this->ChargingSessionManager->method('validateChargingSessionData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->chargingSessionController->startSession($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
    }

    // ============== CDR Data =================

    public function testGetCdrDataReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $request = Request::create("/charging/session/$sessionId/cdr", Request::METHOD_GET, [], [], [], $headers, $data);
        //$this->ChargingSessionManager->method('validateChargingSessionData')->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->ChargingSessionManager->method('getCdrData')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));

        $response = $this->chargingSessionController->getCdrData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid value', $response->getContent());
    }

    public function testGetCdrDataReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['valid_param' => 'value']);
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $request = Request::create("/charging/session/$sessionId/cdr", Request::METHOD_GET, [], [], [], $headers, $data);

        //$this->ChargingSessionManager->method('validateChargingSessionData')->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            'data' => "idSession"
        ];

        $this->ChargingSessionManager->method('getCdrData')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->chargingSessionController->getCdrData($sessionId, $request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"data":"idSession"}}', $response->getContent());
    }

    // ===================================

    public function testAccountDeleteSuccessfully()
    {
        $request = Request::create(
            '/user/unsubscribe', 
            'POST',              
            [],                  
            [],                  
            [],                  
            ['accessToken' => 'valid-token'], 
            json_encode([        
                'reasonId' => '123',
                'otherReason' => 'Testing delete account'
            ])
        );
    
        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);

        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['data' => true],
            'code' => 200
        ]);
        $this->ChargingSessionManager
            ->method('deleteAccount')
            ->willReturn($responseArrayFormat);

        $response = $this->chargingSessionController->accountDelete($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(
            ['data' => true],
            json_decode($response->getContent(), true)
        );
    }

    public function testAccountDeleteValidationError()
    {
        $request = Request::create(
            '/user/unsubscribe', 
            'POST',              
            [],                  
            [],                  
            [],                  
            ['accessToken' => 'valid-token'], 
            json_encode([        
                'reasonId' => '' 
            ])
        );

        $violation = new ConstraintViolation(
            'This field is required.', 
            null,                      
            [],                        
            null,                      
            'reasonId',                
            ''                         
        );

        $violations = new ConstraintViolationList([$violation]);

        $this->ChargingSessionManager
            ->method('validateAccountDeleteData')
            ->willReturn($violations);
        $response = $this->chargingSessionController->accountDelete($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertEquals(
            [
                'error' => [
                    'message' => 'validation_failed',
                    'errors' => [
                        'reasonId' => 'This field is required.'
                    ]
                ]
            ],
            json_decode($response->getContent(), true)
        );
    }

    public function testWalletDetailSuccess(): void
    {
        // Arrange
        $request = Request::create(
            '/user/wallet',
            'GET',
            [],
            [],
            [],
            ['HTTP_accessToken' => 'valid-token']
        );

        // Mock ConstraintViolationListInterface
        $violationListMock = $this->createMock(ConstraintViolationListInterface::class);

        $this->ChargingSessionManager
            ->method('walletDataValidation')
            ->willReturn($violationListMock);

        $responseArrayFormat = $this->createMock(ResponseArrayFormat::class);
        $responseArrayFormat->method('toArray')->willReturn([
            'content' => ['totalCredits' => 0],
            'code' => 200
        ]);
        $this->ChargingSessionManager
            ->method('getWalletDetail')
            ->willReturn($responseArrayFormat);
        $response = $this->chargingSessionController->walletDetail($request, $this->ChargingSessionManager);
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals(
            ['totalCredits' => 0],
            json_decode($response->getContent(), true)
        );
    }

    public function testWalletDetailError(): void
    {
        // Arrange
        $request = Request::create(
            '/user/wallet',
            'GET',
            [],
            [],
            [],
            ['HTTP_accessToken' => 'valid-token']
        );

        // Mock ConstraintViolationListInterface
        $violationListMock = $this->createMock(ConstraintViolationListInterface::class);

        $this->ChargingSessionManager
            ->method('walletDataValidation')
            ->willReturn($violationListMock);

        $this->ChargingSessionManager
            ->method('getWalletDetail')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));
        $response = $this->chargingSessionController->walletDetail($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals(["error" => ["message" => "Invalid value"]], json_decode($response->getContent(), true));
    }

    public function testWalletDetailValidationError(): void
    {
        // Arrange
        $request = Request::create(
            '/user/wallet',
            'GET',
            [],
            [],
            [],
            ['HTTP_accessToken' => 'invalid-token']
        );

        // Mock ConstraintViolation
        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            [],
            null,
            'accessToken',
            null
        );
        $violationList = new ConstraintViolationList([$constraint]);

        // Mock ChargingSessionManager to return validation errors
        $this->ChargingSessionManager
            ->method('walletDataValidation')
            ->willReturn($violationList);

        $response = $this->chargingSessionController->walletDetail($request, $this->ChargingSessionManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertEquals(
            ["error" => [
                "message" => "validation_failed",
                "errors" => [
                "accessToken" => "This value should not be blank."]
            ]],
            json_decode($response->getContent(), true)
        );
    }
}
