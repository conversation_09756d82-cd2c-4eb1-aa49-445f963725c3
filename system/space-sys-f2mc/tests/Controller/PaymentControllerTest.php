<?php

use App\Controller\PaymentController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\PaymentManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class PaymentControllerTest extends WebTestCase
{
    private $paymentController;
    private $paymentManager;

    protected function setUp(): void
    {
        $this->paymentManager = $this->createMock(PaymentManager::class);
        $this->paymentController = new PaymentController($this->paymentManager);
        $this->paymentController->setContainer(static::getContainer());
    }

    // ========= ADD =========

    public function testAddUrlReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $request = Request::create('/user/payments-method', Request::METHOD_GET, [], [], [], $headers, []);

        $this->paymentManager->method('addUrl')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_BAD_REQUEST));

        $response = $this->paymentController->addUrl($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testAddUrlReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $request = Request::create('/user/payments-method', Request::METHOD_GET, [], [], [], $headers, []);

        $expectedResponse = [
            "message" => "The payment is successfully deleted"
        ];

        $this->paymentManager->method('addUrl')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->paymentController->addUrl($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"message":"The payment is successfully deleted"}}', $response->getContent());
    }


}
