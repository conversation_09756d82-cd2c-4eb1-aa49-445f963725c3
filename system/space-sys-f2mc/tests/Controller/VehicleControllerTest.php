<?php

use App\Controller\VehicleController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\VehicleManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class VehicleControllerTest extends WebTestCase
{
    private $vehicleController;
    private $vehicleManager;

    protected function setUp(): void
    {
        $this->vehicleManager = $this->createMock(VehicleManager::class);
        $this->vehicleController = new VehicleController($this->vehicleManager);
        $this->vehicleController->setContainer(static::getContainer());
    }

    // =========== UPDATE ===========
    public function testUpdateReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['random_param' => 'value']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $headers, $data);
        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->vehicleManager->method('update')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid value', $response->getContent());
    }

    public function testUpdateReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['valid_param' => 'value']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $headers, $data);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            "message" => "Successfully modified vehicle"
        ];

        $this->vehicleManager->method('update')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"message":"Successfully modified vehicle"}}', $response->getContent());
    }

    public function testUpdateReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId . '/default', Request::METHOD_PUT, [], [], [], $headers, $data);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['data'],
            null,
            'data',
            null
        );

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->vehicleController->update($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
    }

    // ========= DELETE =========

    public function testDeleteReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $headers, []);
        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->vehicleManager->method('delete')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testDeleteReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $vehicleId = '123';
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $headers, []);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            "message" => "The vehicle is successfully deleted"
        ];

        $this->vehicleManager->method('delete')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"message":"The vehicle is successfully deleted"}}', $response->getContent());
    }

    public function testDeleteReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $vehicleId = 'abc';
        $request = Request::create('/user/vehicles/' . $vehicleId, Request::METHOD_DELETE, [], [], [], $headers, []);

        $constraint = new ConstraintViolation(
            'This value should not be number.',
            null,
            ['data'],
            null,
            'data',
            null
        );

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->vehicleController->delete($vehicleId, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
    }

    // ========= GET LIST =========

    public function testGetListReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $request = Request::create('/user/vehicles', Request::METHOD_GET, [], [], [], $headers, []);

        $this->vehicleManager->method('getList')
            ->willReturn(new ErrorResponse('Unauthorized', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Unauthorized', $response->getContent());
    }

    public function testGetListReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json', 'HTTP_accessToken' => ['random_access_token']];
        $request = Request::create('/user/vehicles', Request::METHOD_GET, [], [], [], $headers, []);

        $expectedResponse = [[
                "id" => 757, 
                "make" => ["id" => 5, "name" => "Jeep"], 
                "model" => ["id" => 5, "name" => "Grand Cherokee 4xe"], 
                "modelYear" => 2024, 
                "plugType" => [["id" => 1, "name" => "J1772" ]], 
                "vin" => "3F9HBEEG4R90116ER" 
            ] 
        ]; 

        $this->vehicleManager->method('getList')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->getList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":[{"id":757,"make":{"id":5,"name":"Jeep"},"model":{"id":5,"name":"Grand Cherokee 4xe"},"modelYear":2024,"plugType":[{"id":1,"name":"J1772"}],"vin":"3F9HBEEG4R90116ER"}]}', $response->getContent());
    }

    // =========== ADD / NEW ===========
    public function testAddReturnsErrorResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['random_param' => 'value']);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $headers, $data);
        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $this->vehicleManager->method('add')
            ->willReturn(new ErrorResponse('Invalid value', Response::HTTP_BAD_REQUEST));

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $this->assertStringContainsString('Invalid value', $response->getContent());
    }

    public function testAddReturnsSuccessfulResponse()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['valid_param' => 'value']);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $headers, $data);

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $expectedResponse = [
            "message" => "Successfully modified vehicle"
        ];

        $this->vehicleManager->method('add')
            ->willReturn(new SuccessResponse($expectedResponse, Response::HTTP_OK));

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"success":{"message":"Successfully modified vehicle"}}', $response->getContent());
    }

    public function testAddReturnsValidationError()
    {
        $headers = ['Content-Type' => 'application/json'];
        $data = json_encode(['invalid_param' => 'value']);
        $request = Request::create('/user/vehicles', Request::METHOD_POST, [], [], [], $headers, $data);

        $constraint = new ConstraintViolation(
            'This value should not be blank.',
            null,
            ['data'],
            null,
            'data',
            null
        );

        $this->vehicleManager->method('validateVehicleData')
            ->willReturn(new ConstraintViolationList([$constraint]));

        $response = $this->vehicleController->add($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $this->assertStringContainsString('validation_failed', $response->getContent());
    }


}
