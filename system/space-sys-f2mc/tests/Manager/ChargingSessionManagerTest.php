<?php

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ChargingSessionManager;
use App\Service\ChargingSessionService;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ChargingSessionManagerTest extends TestCase
{
    private $ChargingSessionService;
    private $ChargingSessionManager;
    private $validator;

    protected function setUp(): void
    {
        $this->ChargingSessionService = $this->createMock(ChargingSessionService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));
        $this->ChargingSessionManager = new ChargingSessionManager($this->ChargingSessionService, $this->validator);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');

        $this->ChargingSessionManager->setLogger($logger);
    }

    public function testStartSessionCreatesSuccessfulResponse()
    {
        $params = ['param1' => 'value1'];
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('startSession')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionManager->startSession($params, $accessToken);


        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['data' => 'idSession'], $result->getData());
    }

    public function testStartSessionReturnsErrorResponseOnFailure()
    {
        $params = ['param1' => 'value1'];
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('startSession')
            ->with($params)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Error"]));

        $result = $this->ChargingSessionManager->startSession($params, $accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testStartSessionHandlesException()
    {
        $params = ['param1' => 'value1'];

        $this->ChargingSessionService->expects($this->once())
            ->method('startSession')
            ->with($params)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->startSession($params, '');

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // ============ CDR Data ==================

    public function testGetCdrDataCreatesSuccessfulResponse()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('getCdrData')
            ->with($sessionId)
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionManager->getCdrData($sessionId, $accessToken);


        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['data' => 'idSession'], $result->getData());
    }

    public function testGetCdrDataReturnsErrorResponseOnFailure()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->ChargingSessionService->expects($this->once())
            ->method('getCdrData')
            ->with($sessionId)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Bad Request"]));

        $result = $this->ChargingSessionManager->getCdrData($sessionId, $accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Bad Request', $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testGetCdrDataHandlesException()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';

        $this->ChargingSessionService->expects($this->once())
            ->method('getCdrData')
            ->with($sessionId)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->getCdrData($sessionId, '');

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // ========================================

    public function testValidateChargingSessionDataReturnsNoViolations()
    {
        $data = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'coordinate' => ['lat' => 12.34, 'lng' => 56.78],
            'connectorId' => 'conn1',
            'response_url' => 'http://example.com',
            'accessToken' => 'accessToken'
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->ChargingSessionManager->validateChargingSessionData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    public function testDeleteAccountSuccess()
    {
        $params = ['reasonId' => '123', 'otherReason' => 'Testing'];
        $token = ['valid-token'];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(['data' => true]);

        $this->ChargingSessionService->expects($this->once())
            ->method('accountDeletion')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->deleteAccount($params, $token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['data' => true], $result->getData());
    }

    public function testDeleteAccountReturnsErrorResponseOnFailure()
    {
        $params = ['param1' => 'value1'];
        $token = ["accessToken"];

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getData')->willReturn(['data' => false]);

        $this->ChargingSessionService->expects($this->once())
            ->method('accountDeletion')
            ->with($params, $token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->deleteAccount($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('{"data":false}', $result->toArray()['content']['error']['message']); 
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testDeleteAccountHandlesException()
    {
        $params = ['param1' => 'value1'];
        $token = ["accessToken"];

        $this->ChargingSessionService->expects($this->once())
            ->method('accountDeletion')
            ->with($params, $token)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->ChargingSessionManager->deleteAccount($params, $token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testvalidateAccountDeleteDataReturnsNoViolations()
    {
        $data = [
            'reasonId' => 123,
            'otherReason' => 'Testing',
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->ChargingSessionManager->validateChargingSessionData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    public function testgetWalletDetailSuccess()
    {
        $token = ['valid-token'];
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(['success' => ['totalCredits' => 0]]);
        $this->ChargingSessionService->expects($this->once())
            ->method('walletDetails')
            ->with($token)
            ->willReturn($mockResponse);
        $result = $this->ChargingSessionManager->getWalletDetail($token);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['success' => ['totalCredits' => 0]], $result->getData());
    }

    public function testgetWalletDetailReturnsErrorResponseOnFailure()
    {
        $token = ["accessToken"];
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_BAD_REQUEST);
        $mockResponse->method('getData')->willReturn("bad request");
        $this->ChargingSessionService->expects($this->once())
            ->method('walletDetails')
            ->with($token)
            ->willReturn($mockResponse);

        $result = $this->ChargingSessionManager->getWalletDetail($token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('bad request', $result->toArray()['content']['error']['message']); 
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testgetWalletDetailHandlesException()
    {
        $token = ["accessToken"];
        $this->ChargingSessionService->expects($this->once())
            ->method('walletDetails')
            ->with($token)
            ->willThrowException(new \Exception('Service error', 500));
        $result = $this->ChargingSessionManager->getWalletDetail($token);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    public function testwalletDataValidationReturnsNoViolations()
    {
        $data = [
            'accessToken' => 'validToken'
        ];
        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);
        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);
        $violations = $this->ChargingSessionManager->walletDataValidation($data);
        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }
}
