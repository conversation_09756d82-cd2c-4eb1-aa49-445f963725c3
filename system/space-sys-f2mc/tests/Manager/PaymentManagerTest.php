<?php

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\PaymentManager;
use App\Service\PaymentService;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PaymentManagerTest extends TestCase
{
    private $paymentService;
    private $paymentManager;
    private $validator;
    private $paymentMapper;

    protected function setUp(): void
    {
        $this->paymentService = $this->createMock(PaymentService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));
        $this->paymentManager = new PaymentManager($this->paymentService, $this->validator, $this->paymentMapper);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');

        $this->paymentManager->setLogger($logger);
    }
    

    // =============== ADD ===============

    public function testAddUrlSuccessfulResponse()
    {
        $accessToken = "accessToken";

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, ["message" => "The payment is successfully deleted"]));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['message' => 'The payment is successfully deleted'], $result->getData());
    }

    public function testAddUrlReturnsErrorResponseOnFailure()
    {
        $accessToken = "accessToken";

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testAddUrlHandlesException()
    {
        $accessToken = "accessToken";

        $this->paymentService->expects($this->once())
            ->method('addUrl')
            ->with($accessToken)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->paymentManager->addUrl($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

}
