<?php

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\VehicleManager;
use App\Service\VehicleService;
use App\DataMapper\VehicleDataMapper;
use Symfony\Component\HttpFoundation\Response;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleManagerTest extends TestCase
{
    private $vehicleService;
    private $vehicleManager;
    private $validator;
    private $vehicleMapper;

    protected function setUp(): void
    {
        $this->vehicleService = $this->createMock(VehicleService::class);
        $this->vehicleMapper = $this->createMock(VehicleDataMapper::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));
        $this->vehicleManager = new VehicleManager($this->vehicleService, $this->validator, $this->vehicleMapper);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())->method('info');
        $logger->expects($this->any())->method('error');

        $this->vehicleManager->setLogger($logger);
    }
    
    // =============== UPDATE ===============

    public function testUpdateSuccessfulResponse()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_OK, ["message" => "Successfully modified vehicle"]));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['message' => 'Successfully modified vehicle'], $result->getData());
    }

    public function testUpdateReturnsErrorResponseOnFailure()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testUpdateHandlesException()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('update')
            ->with($data)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->update($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // =============== DELETE ===============

    public function testDeleteSuccessfulResponse()
    {
        $data = [
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_OK, ["message" => "The vehicle is successfully deleted"]));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['message' => 'The vehicle is successfully deleted'], $result->getData());
    }

    public function testDeleteReturnsErrorResponseOnFailure()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testDeleteHandlesException()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('delete')
            ->with($data)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->delete($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // =============== VALIDATE VEHICLE ===============

    public function testValidateVehicleDataReturnsNoViolations()
    {
        $data = [
            'vin' => 'FCABEF567IND00179',
            'accessToken' => 'accessToken'
        ];

        $mockViolations = $this->createMock(ConstraintViolationListInterface::class);

        $this->validator->expects($this->once())
            ->method('validate')
            ->with($data, \PHPUnit\Framework\anything())
            ->willReturn($mockViolations);

        $violations = $this->vehicleManager->validateVehicleData($data);

        $this->assertInstanceOf(ConstraintViolationListInterface::class, $violations);
    }

    // =============== GET LIST ===============

    public function testGetListSuccessfulResponse()
    {
        $accessToken = "accessToken";

        $expectedResponse = ["data" => [[
                "id" => 757, 
                "make" => ["id" => 5, "name" => "Jeep"], 
                "model" => ["id" => 5, "name" => "Grand Cherokee 4xe"], 
                "modelYear" => 2024, 
                "plugType" => [["id" => 1, "name" => "J1772" ]], 
                "vin" => "3F9HBEEG4R90116ER" 
            ]] 
        ]; 
        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_OK, $expectedResponse));

        $this->vehicleMapper->expects($this->once())
            ->method('map')
            ->with($expectedResponse['data'])
            ->willReturn($expectedResponse['data']);

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals($expectedResponse['data'], $result->getData());
    }

    public function testGetListReturnsErrorResponseOnFailure()
    {
        $accessToken = "accessToken";

        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Request failed"]));

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Request failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testGetListHandlesException()
    {
        $accessToken = "accessToken";

        $this->vehicleService->expects($this->once())
            ->method('getList')
            ->with($accessToken)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->getList($accessToken);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

    // =============== ADD / NEW ===============

    public function testAddSuccessfulResponse()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_OK, ["message" => "Vehicle added with Success"]));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['message' => 'Vehicle added with Success'], $result->getData());
    }

    public function testAddReturnsErrorResponseOnFailure()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ["message" => "Operation failed"]));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals("Operation failed", $result->toArray()['content']['error']['message']);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->toArray()['code']);
    }

    public function testAddHandlesException()
    {
        $data = [
            'params' => ['param1' => 'value1'],
            'accessToken' => "accessToken"
        ];

        $this->vehicleService->expects($this->once())
            ->method('add')
            ->with($data)
            ->willThrowException(new \Exception('Service error', 500));

        $result = $this->vehicleManager->add($data);

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals('Service error', $result->toArray()['content']['error']['message']);
        $this->assertEquals(500, $result->toArray()['code']);
    }

}
