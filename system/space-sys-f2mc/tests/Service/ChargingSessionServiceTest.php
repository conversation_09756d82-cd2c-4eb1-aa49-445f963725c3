<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\ChargingSessionService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ChargingSessionServiceTest extends TestCase
{
    private $f2mConnector;
    private $ChargingSessionService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->ChargingSessionService = new ChargingSessionService($this->f2mConnector);
    }

    public function testStartSessionCreatesSuccessfulResponse()
    {
        $payload = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'coordinate' => ['lat' => 12.34, 'lng' => 56.78],
            'connectorId' => 'conn1',
            'response_url' => 'http://example.com'
        ];
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/charging-sessions/start',
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionService->startSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals(["data" => "idSession"], $result->getData());
    }

    public function testStartSessionHandlesErrorResponse()
    {
        $payload = [
            'locationId' => 'loc1',
            'evseId' => 'evse1',
            'coordinate' => ['lat' => 12.34, 'lng' => 56.78],
            'connectorId' => 'conn1',
            'response_url' => 'http://example.com'
        ];
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_POST,
                'api/v1/charging-sessions/start',
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->ChargingSessionService->startSession($payload, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    public function testGetCdrDataCreatesSuccessfulResponse()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v1/charging-sessions/$sessionId/cdr",
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_CREATED, ["data" => "idSession"]));

        $result = $this->ChargingSessionService->getCdrData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_CREATED, $result->getCode());
        $this->assertEquals("idSession", $result->getData());
    }

    public function testGetCdrDataHandlesErrorResponse()
    {
        $sessionId = '3377f8kl-49e1-8d2t-0367-9b7174933845';
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_GET,
                "api/v1/charging-sessions/$sessionId/cdr",
                [
                    'headers' => [
                        'token' => $accessToken,
                        'Content-Type' => 'application/json'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->ChargingSessionService->getCdrData($sessionId, $accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    public function testAccountDeletionSuccess()
    {
        $payload = ['reasonId' => '123', 'otherReason' => 'Testing'];
        $accessToken = 'valid-token';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_CREATED);
        $mockResponse->method('getData')->willReturn(["data" => 'true']);

        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_PUT,
            "api/v1/user/delete",
            [
                'headers' => [
                    'token' => $accessToken, 
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload,
            ]
        )
        ->willReturn($mockResponse);

        $response = $this->ChargingSessionService->accountDeletion($payload, $accessToken);

        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_CREATED, $response->getCode());
        $this->assertEquals(true, $response->getData());
    }
    public function testAccountDeletionHandlesErrorResponse()
    {
        $payload = ['reasonId' => '123', 'otherReason' => 'Testing'];
        $accessToken = 'valid-token';

        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_UNAUTHORIZED);
        $mockResponse->method('getData')->willReturn(["message" => "Invalid token."]);

        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_PUT,
            "api/v1/user/delete",
            [
                'headers' => [
                    'token' => $accessToken, 
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload,
            ]
        )
        ->willReturn($mockResponse);

        $response = $this->ChargingSessionService->accountDeletion($payload, $accessToken);
        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getCode());
        $this->assertEquals(["message" => "Invalid token."], $response->getData());
    }

    public function testWalletDetailsSuccess()
    {
        $accessToken = 'valid-token';
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_OK);
        $mockResponse->method('getData')->willReturn(["data" => ['totalCredits' => 0]]);
        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_GET,
            "api/v1/payment/wallet/summary",
            [       
                'headers' => [
                    'token' => $accessToken, 
                    'Content-Type' => 'application/json'
                ]
            ]
        )
        ->willReturn($mockResponse);
        $response = $this->ChargingSessionService->walletDetails( $accessToken);
        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals(['totalCredits' => 0], $response->getData());
    }

    public function testWalletDetailsHandlesErrorResponse()
    {
        $accessToken = 'valid-token';
        $mockResponse = $this->createMock(WSResponse::class);
        $mockResponse->method('getCode')->willReturn(Response::HTTP_UNAUTHORIZED);
        $mockResponse->method('getData')->willReturn(["message" => "Invalid token."]);
        $this->f2mConnector->expects($this->once())
        ->method('callF2mc')
        ->with(
            Request::METHOD_GET,
            "api/v1/payment/wallet/summary",
            [
                'headers' => [
                    'token' => $accessToken, 
                    'Content-Type' => 'application/json'
                ]
            ]
        )
        ->willReturn($mockResponse);
        $response = $this->ChargingSessionService->walletDetails($accessToken);
        // Assert
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getCode());
        $this->assertEquals(["message" => "Invalid token."], $response->getData());
    }
}
