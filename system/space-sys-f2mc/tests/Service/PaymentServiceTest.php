<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\PaymentService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PaymentServiceTest extends TestCase
{
    private $f2mConnector;
    private $paymentService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->paymentService = new PaymentService($this->f2mConnector, 'clientId', 'clientSecret');
    }

    // =============== ADD ===============

    public function testAddUrlCreatesSuccessfullResponse()
    {
        $accessToken = "accessToken";
        
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_PUT,
                "api/payment-method",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ["data" => true]));

        $result = $this->paymentService->addUrl($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals(["data" => true], $result->getData());
    }

    public function testAddUrlHandlesErrorResponse()
    {
        $accessToken = "accessToken";
        
        $this->f2mConnector->expects($this->once())
            ->method('callF2mc')
            ->with(
                Request::METHOD_PUT,
                "api/payment-method",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->paymentService->addUrl($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

}
