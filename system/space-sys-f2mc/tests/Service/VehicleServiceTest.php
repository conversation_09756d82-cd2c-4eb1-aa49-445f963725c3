<?php

namespace App\Tests\Service;

use App\Connector\F2mConnector;
use App\Helper\WSResponse;
use App\Service\VehicleService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class VehicleServiceTest extends TestCase
{
    private $f2mConnector;
    private $vehicleService;

    protected function setUp(): void
    {
        $this->f2mConnector = $this->createMock(F2mConnector::class);
        $this->vehicleService = new VehicleService($this->f2mConnector, 'clientId', 'clientSecret');
    }

    // =============== UPDATE ===============

    public function testUpdateCreatesSuccessfulResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_PATCH,
                "api/v1/user/vehicles/{$vehicleId}/default",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ["data" => true]));

        $result = $this->vehicleService->update($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals(["data" => true], $result->getData());
    }

    public function testUpdateHandlesErrorResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_PATCH,
                "api/v1/user/vehicles/{$vehicleId}/default",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->vehicleService->update($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    // =============== DELETE ===============

    public function testDeleteCreatesSuccessfulResponse()
    {
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_DELETE,
                "api/v1/user/vehicles/{$vehicleId}",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ["data" => true]));

        $result = $this->vehicleService->delete($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals(["data" => true], $result->getData());
    }

    public function testDeleteHandlesErrorResponse()
    {
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_DELETE,
                "api/v1/user/vehicles/{$vehicleId}",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->vehicleService->delete($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    // =============== GET LIST ===============

    public function testGetListCreatesSuccessfulResponse()
    {
        $accessToken = "accessToken";

        $expectedResponse = ["data" => [[
                "id" => 757, 
                "make" => ["id" => 5, "name" => "Jeep"], 
                "model" => ["id" => 5, "name" => "Grand Cherokee 4xe"], 
                "modelYear" => 2024, 
                "plugType" => [["id" => 1, "name" => "J1772" ]], 
                "vin" => "3F9HBEEG4R90116ER" 
            ]] 
        ]; 

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                "api/v1/user/vehicles",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, $expectedResponse));

        $result = $this->vehicleService->getList($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals($expectedResponse, $result->getData());
    }

    public function testGetListHandlesErrorResponse()
    {
        $accessToken = "accessToken";

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_GET,
                "api/v1/user/vehicles",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ]
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->vehicleService->getList($accessToken);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

    // =============== ADD / NEW ===============

    public function testAddCreatesSuccessfulResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $data = [
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                "api/v1/user/vehicles",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_OK, ["data" => true]));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_OK, $result->getCode());
        $this->assertEquals(["data" => true], $result->getData());
    }

    public function testAddHandlesErrorResponse()
    {
        $payload = [
            'vin' => 'FCABEF567IND00179'
        ];
        $accessToken = "accessToken";
        $vehicleId = '123';
        $data = [
            'vehicleId' => $vehicleId,
            'accessToken' => $accessToken,
            'params' => $payload
        ];

        $this->f2mConnector->expects($this->once())
            ->method('call')
            ->with(
                Request::METHOD_POST,
                "api/v1/user/vehicles",
                [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'clientId' => 'clientId',
                        'clientSecret' => 'clientSecret',
                        'Authorization' => 'Bearer accessToken'
                    ],
                    'json' => $payload,
                ]
            )
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, ['error' => 'Invalid request']));

        $result = $this->vehicleService->add($data);

        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $result->getCode());
        $this->assertEquals(['error' => 'Invalid request'], $result->getData());
    }

}
