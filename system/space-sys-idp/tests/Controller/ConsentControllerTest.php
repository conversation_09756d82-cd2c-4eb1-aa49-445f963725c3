<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Controller\ConsentController;
use App\Helper\SuccessResponse;
use App\Manager\ConsentManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ConsentControllerTest extends WebTestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $container->get(ValidatorInterface::class);
    }

    public function testGetConsentInfosReturnsError()
    {
        $consentManager = $this->createMock(ConsentManager::class);
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/accounts/info', 'GET', []);
        $expected["error"] = [
            "message" => "validation_failed"
        ];

        $response = $controller->getAccountsInfo($this->validator, $consentManager, $request);
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testGetConsentInfosReturnsSuccess()
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);

        $consentManager = $this->createMock(ConsentManager::class);
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $headerBag = $this->createMock(HeaderBag::class);

        $headerBag->expects($this->any())
            ->method('get')
            ->willReturn('userId');
        $request->headers = $headerBag;

        $validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $consentManager->expects($this->once())
            ->method('getAccountsInfo')
            ->willReturn(new SuccessResponse([], Response::HTTP_OK));

        $response = $controller->getAccountsInfo($validator, $consentManager, $request);
        $expectedResponse = new JsonResponse(['success' => []], Response::HTTP_OK);
        $this->assertSame($response->getContent(), $expectedResponse->getContent());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testPutConsentInfosReturnsError()
    {
        $consentManager = $this->createMock(ConsentManager::class);
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/accounts/info', 'POST', []);
        $expected["error"] = [
            "message" => "validation_failed"
        ];

        $response = $controller->getAccountsInfo($this->validator, $consentManager, $request);
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testPutConsentInfosReturnsSuccess()
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);

        $consentManager = $this->createMock(ConsentManager::class);
        $controller = new ConsentController();
        $controller->setContainer(static::getContainer());

        $headerBag = $this->createMock(HeaderBag::class);
        $queryBag  = $this->createMock(ParameterBag::class);

        $headerBag->expects($this->any())
            ->method('get')
            ->with('userId')
            ->willReturn('123');

        $queryBag->expects($this->any())
            ->method('get')
            ->with('payload')
            ->willReturn('{}');
            
        $request->headers = $headerBag;
        $request->query = $queryBag;

        $validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $consentManager->expects($this->once())
            ->method('setAccountsInfo')
            ->willReturn(new SuccessResponse([], Response::HTTP_OK));

        $response = $controller->putAccountsInfo($validator, $consentManager, $request);
        $expectedResponse = new JsonResponse(['success' => []], Response::HTTP_OK);
        
        $this->assertSame($response->getContent(), $expectedResponse->getContent());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }
    
}
