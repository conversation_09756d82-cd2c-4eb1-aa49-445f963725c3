<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Controller\UserController;
use App\Helper\SuccessResponse;
use App\Manager\UserManager;
use App\Service\CVSManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserControllerTest extends WebTestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        $this->validator = $container->get(ValidatorInterface::class);
    }

    public function testGetUserReturnsError()
    {
        $userManager = $this->createMock(UserManager::class);
        $controller = new UserController();
        $controller->setContainer(static::getContainer());
        $userManager = $this->createMock(UserManager::class);
        $controller = new UserController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/users', 'GET', []);
        $request = Request::create('/v1/users', 'GET', []);
        $expected["error"] = [
            "message" => "validation_failed"
        ];

        $response = $controller->index($this->validator, $userManager, $request);
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testGetUserReturnsSuccess()
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);
        $userManager = $this->createMock(UserManager::class);
        $controller = new UserController();
        $controller->setContainer(static::getContainer());
        $headerBag = $this->createMock(HeaderBag::class);

        $headerBag->expects($this->any())
            ->method('get')
            ->willReturn('userCvsId');
        $request->headers = $headerBag;

        $validator->expects($this->any())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $userManager->expects($this->once())
            ->method('getUser')
            ->willReturn(new SuccessResponse(['userId' => '123'], Response::HTTP_OK));

        $response = $controller->index($validator, $userManager, $request);
        $expectedResponse = new JsonResponse(['success' => ['userId' => '123']], Response::HTTP_OK);
        $this->assertSame($response->getContent(), $expectedResponse->getContent());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testGetCvsIdReturnsError()
    {
        $cvsManager = $this->createMock(CVSManager::class);
        $controller = new UserController();
        $controller->setContainer(static::getContainer());

        $request = Request::create('/v1/introspection', 'GET', []);
        $expected["error"] = [
            "message" => "validation_failed"
        ];

        $response = $controller->introspection($this->validator, $cvsManager, $request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals($expected, json_decode($response->getContent(), true));
    }

    public function testGetCvsIdReturnsSuccess()
    {
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);
        $cvsManager = $this->createMock(CVSManager::class);
        $controller = new UserController();
        $controller->setContainer(static::getContainer());
        $headerBag = $this->createMock(HeaderBag::class);
        $queryBag = $this->createMock(ParameterBag::class);

        $headerBag->expects($this->any())
            ->method('get')
            ->willReturn('cvsToken');
        $queryBag->expects($this->any())
            ->method('get')
            ->willReturn('brand');
        $request->headers = $headerBag;
        $request->query = $queryBag;

        $validator->expects($this->once())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $cvsManager->expects($this->once())
            ->method('getCvsId')
            ->willReturn(new SuccessResponse(['cvsId' => '123'], Response::HTTP_OK));

        $response = $controller->introspection($validator, $cvsManager, $request);
        $expectedResponse = new JsonResponse(['success' => ['cvsId' => '123']], Response::HTTP_OK);
        $this->assertSame($response->getContent(), $expectedResponse->getContent());
        $this->assertInstanceOf(JsonResponse::class, $response);
    }
}
