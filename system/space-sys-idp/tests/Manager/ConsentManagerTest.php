<?php

namespace App\Tests\Manager;

use App\Connector\GigyaApiConnector;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\ConsentManager;
use App\Service\ConsentService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class ConsentManagerTest extends TestCase
{

    /**
     * @var ConsentManager
     */
    private $consentManager;

    /**
     * @var ConsentService
     */
    private $consentService;

    /**
     * @var GigyaApiConnector
     */
    private $connector;

    public function setUp(): void
    {        
        $this->connector = $this->createMock(GigyaApiConnector::class);
        $this->consentService = new ConsentService($this->connector, 'url-test', 'api-key-test');
        $this->consentManager = new ConsentManager($this->consentService);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
            ;
        $logger->expects($this->any())
            ->method('error')
            ;
        $this->consentManager->setLogger($logger);
    }

    public function testGetAccountsInfo() {
        $userId = '123';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, 
            json_decode('{"callId":"XXX","errorCode":0,"apiVersion":2,"statusCode":200,"statusReason":"OK","time":"2024-10-17T00:30:52.709Z","UID":"XXXX","created":"2023-08-16T07:36:41.978Z","createdTimestamp":*************,"preferences":{"marketing":{"isConsentGranted":true,"docDate":"2019-03-04T00:00:00.000Z","lang":"","lastConsentModified":"2024-10-16T15:14:37.944Z","actionTimestamp":"2024-10-16T15:14:37.944Z","tags":["DefaultLocale(en)"],"customData":[],"entitlements":[]},"profiling":{"isConsentGranted":false,"docDate":"2019-03-04T00:00:00.000Z","lang":"","lastConsentModified":"2024-10-16T20:19:04.514Z","actionTimestamp":"2024-10-16T20:19:04.514Z","tags":["DefaultLocale(en)"],"customData":[],"entitlements":[]},"thirdPartiesMarketing":{"isConsentGranted":true,"docDate":"2019-05-15T00:00:00.000Z","lang":"","lastConsentModified":"2024-10-16T15:14:37.944Z","actionTimestamp":"2024-10-16T15:14:37.944Z","tags":["DefaultLocale(en)"],"customData":[],"entitlements":[]},"thirdPartiesProfiling":{"isConsentGranted":false,"docDate":"2019-05-03T00:00:00.000Z","lang":"","lastConsentModified":"2024-10-16T20:19:04.514Z","actionTimestamp":"2024-10-16T20:19:04.514Z","tags":["DefaultLocale(en)"],"customData":[],"entitlements":[]}},"isActive":true,"isRegistered":true,"isVerified":true,"lastUpdated":"2024-10-16T20:20:09.209Z","lastUpdatedTimestamp":*************}',true)));
        $response = $this->consentManager->getAccountsInfo($userId);
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertIsArray($response->toArray());
        $this->assertEquals(Response::HTTP_OK, $response->toArray()['code']);
        $responseArray['success'] = [
            "preferences" => [
                "marketing" => [
                "isConsentGranted" => true 
                ], 
                "profiling" => [
                    "isConsentGranted" => false 
                ], 
                "thirdPartiesMarketing" => [
                    "isConsentGranted" => true 
                ], 
                "thirdPartiesProfiling" => [
                    "isConsentGranted" => false 
                ] 
            ]
        ];
        $this->assertEquals($responseArray, $response->toArray()['content']);
    }

    public function testPutAccountsInfo() {
        $userId = '123';
        $payload = json_decode('{"marketing":{"isConsentGranted":true},"profiling":{"isConsentGranted":true},"thirdPartiesMarketing":{"isConsentGranted":true},"thirdPartiesProfiling":{"isConsentGranted":true}}', true);
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, 
            json_decode('{"callId":"XXX","errorCode":0,"apiVersion":2,"statusCode":200,"statusReason":"OK","time":"2024-10-16T15:14:38.463Z"}',true)));
        $response = $this->consentManager->setAccountsInfo($userId, $payload);
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertIsArray($response->toArray());
        $this->assertEquals(Response::HTTP_OK, $response->toArray()['code']);
        $responseArray['success'] = [
            "callId" => "XXX", 
            "errorCode" => 0, 
            "apiVersion" => 2, 
            "statusCode" => 200, 
            "statusReason" => "OK", 
            "time" => "2024-10-16T15:14:38.463Z" 
        ];
        $this->assertEquals($responseArray, $response->toArray()['content']);
    }

}
