<?php

namespace App\Tests\Manager;

use App\Connector\GigyaApiConnector;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Manager\UserManager;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class UserManagerTest extends TestCase
{

    /**
     * @var UserManager
     */
    private $userManager;

    /**
     * @var UserService
     */
    private $userService;

    /**
     * @var GigyaApiConnector
     */
    private $connector;

    public function setUp(): void
    {        
        $this->connector = $this->createMock(GigyaApiConnector::class);
        $this->userService = new UserService($this->connector, 'url-test', 'api-key-test');
        $this->userManager = new UserManager($this->userService);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
            ;
        $logger->expects($this->any())
            ->method('error')
            ;
        $this->userManager->setLogger($logger);
    }

    public function testGetUserOk()
    {
        $userCvsId = 'SP-ACNT200000088042';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, json_decode('{"statusCode":"200","results":[{"data":{"CVSID":{"spoticar":"SP-ACNT200000088042"}},"profile":{"lastName":"Doe","firstName":"Joe","email":"<EMAIL>"},"UID":"e8b9edb8e6960b78ba404e0b0b83e7ca"}]}',true)));
        $response = $this->userManager->getUser($userCvsId);
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertIsArray($response->getData());
        $this->assertEquals(Response::HTTP_OK, $response->toArray()['code']);
        $responseArray['success'] = [
            'userId' => 'e8b9edb8e6960b78ba404e0b0b83e7ca',
            'userPsaId' => ['spoticar' => 'SP-ACNT200000088042'],
            'profile' => [
                'lastName' => 'Doe',
                'firstName' => 'Joe',
                'email' => '<EMAIL>'
            ]
        ];
        $this->assertEquals($responseArray, $response->toArray()['content']);
    }

    public function testGetUserWrongBrand()
    {
        $userCvsId = 'RANDOM-NOT404FOUND';
        
        $response = $this->userManager->getUser($userCvsId);
        $this->assertInstanceOf(ErrorResponse::class, $response);
        $this->assertEquals(500, $response->toArray()['code']);
    }

    public function testGetUserNotOk()
    {
        $userCvsId = 'SP-NOT404FOUND';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, []));
        $response = $this->userManager->getUser($userCvsId);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    public function testGetUserException()
    {
        $userCvsId = 'SP-NOT404FOUND';

        $this->connector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception());

        $response = $this->userManager->getUser($userCvsId);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

}
