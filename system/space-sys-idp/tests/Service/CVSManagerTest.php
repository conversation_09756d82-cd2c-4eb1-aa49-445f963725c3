<?php
namespace App\Tests\Service;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;
use App\Service\CVSManager;
use App\Service\CVSService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class CVSManagerTest extends TestCase
{
    /**
     * @var CVSManager
     */
    private $cvsManager;

    /**
     * @var CVSService
     */
    private $cvsService;

    public function setUp(): void
    {
        $this->cvsService = $this->createMock(CVSService::class);
        $this->cvsManager = new CVSManager($this->cvsService);

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info')
        ;
        $logger->expects($this->any())
            ->method('error')
        ;
        $this->cvsManager->setLogger($logger);
    }

    public function testGetCvsIdReturnsSuccess()
    {
        $this->cvsService->expects($this->once())
            ->method('introspectUserInfos')
            ->with('token', 'brand')
            ->willReturn(new WSResponse(Response::HTTP_OK, ['123']));
        $result = $this->cvsManager->getCvsId('token', 'brand', 'SPACEWEB', '', '');
        $this->assertInstanceOf(SuccessResponse::class, $result);
        $this->assertEquals(['123'], $result->getData());
    }

    public function testGetCvsIdReturnsError()
    {
        $this->cvsService->expects($this->once())
            ->method('introspectUserInfos')
            ->with('token', 'brand')
            ->willReturn(new WSResponse(Response::HTTP_UNAUTHORIZED, []));
        $result = $this->cvsManager->getCvsId('token', 'brand', 'SPACEWEB', '', '');
        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $result->toArray()['code']);
        $this->assertEquals('unauthorized', $result->toArray()['content']['error']['message']);
    }

    public function testGetCvsIdReturnsException()
    {
        $this->cvsService->expects($this->once())
            ->method('introspectUserInfos')
            ->with('token', 'brand')
            ->willThrowException(new \Exception('API error', 500));
        $result = $this->cvsManager->getCvsId('token', 'brand', 'SPACEWEB', '', '');

        $this->assertInstanceOf(ErrorResponse::class, $result);
        $this->assertEquals(500, $result->toArray()['code']);
        $this->assertEquals('API error', $result->toArray()['content']["error"]['message']);
    }
}
