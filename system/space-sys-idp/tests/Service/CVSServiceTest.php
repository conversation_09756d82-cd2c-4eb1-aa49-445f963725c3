<?php

namespace App\Tests\Service;

use App\Helper\ErrorResponse;
use App\Helper\WSResponse;
use App\Service\CVSApiConnector;
use App\Service\CVSService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class CVSServiceTest extends TestCase
{
    /** @var CVSService */
    private $cvsService;

    /** @var CVSApiConnector */
    private $connector;

    /** @var string */
    private $filePath;

    public function setUp(): void
    {
        $this->connector = $this->createMock(CVSApiConnector::class);
        $this->filePath = __DIR__;
        $this->cvsService = new CVSService($this->filePath, $this->connector);
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->any())
            ->method('info');
        $logger->expects($this->any())
            ->method('error');
        $this->cvsService->setLogger($logger);
    }

    public function testGetCvsIdSuccess(): void
    {
        $cvsToken = '85e76a40-8e38-4ce1-969b-e4940d76a9f0';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, ['active' => true, 'user_id' => "AP-1234", 'exp' => 123]));

        $mockData = [
            'spaceweb' => [
                'AP' => [
                    'groups' => [
                        'default' => [
                            'client_id' => 'client_id_default',
                            'secret' => 'secret_default',
                        ],
                    ],
                ],
            ],
        ];

        file_put_contents($this->filePath . '/cvs_spaceweb_auth.json', json_encode($mockData));
        $response = $this->cvsService->introspectUserInfos($cvsToken, 'AP', 'SPACEWEB', '', '');
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetCvsIdError()
    {
        $cvsToken = '85e76a40-8e38-4ce1-969b-e4940d76a9f0';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, 'Bad Request'));

        $response = $this->cvsService->introspectUserInfos($cvsToken, 'AP', 'SPACEWEB', '', '');
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
        $this->assertEquals("Bad Request", $response->getData());
    }
    public function testGetCvsIdException()
    {
        $cvsToken = '85e76a40-8e38-4ce1-969b-e4940d76a9f0';
        $this->connector->expects($this->once())
            ->method('call')
            ->willThrowException(new \Exception('API error', 500));

        $result = $this->cvsService->introspectUserInfos($cvsToken, 'AP', 'SPACEWEB', '', '');
        @unlink($this->filePath . '/cvs_spaceweb_auth.json');
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(500, $result->getCode());
        $this->assertEquals('API error', $result->getData());
    }
}
