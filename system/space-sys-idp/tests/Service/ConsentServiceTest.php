<?php

namespace App\Tests\Service;

use App\Connector\GigyaApiConnector;
use App\Helper\WSResponse;
use App\Service\ConsentService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ConsentServiceTest extends TestCase
{
    /**
     * @var ConsentService
     */
    private $consentService;

    /**
     * @var GigyaApiConnector|\PHPUnit\Framework\MockObject\MockObject
     */
    private $connector;

    public function setUp(): void
    {
        $this->connector = $this->createMock(GigyaApiConnector::class);
        $this->consentService = new ConsentService($this->connector, 'gigya-user-key-test', 'gigya-secret-test');
    }

    public function testGetAccountInfoError()
    {
        $userId = '';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, "Bad request"));
        $response = $this->consentService->getAccountsInfo($userId);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
        $this->assertEquals("Bad request", $response->getData());
    }

    public function testPutAccountInfoError()
    {
        $userId = '';        
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_BAD_REQUEST, 'Bad request'));
        $response = $this->consentService->setAccountsInfo($userId, []);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
        $this->assertEquals("Bad request", $response->getData());
    }
}
