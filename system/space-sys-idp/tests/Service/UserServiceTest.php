<?php

namespace App\Tests\Service;

use App\Connector\GigyaApiConnector;
use App\Helper\ErrorResponse;
use App\Helper\WSResponse;
use App\Service\GigyaQueryService;
use App\Service\UserService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class UserServiceTest extends TestCase
{
    /**
     * @var UserService
     */
    private $userService;

    /**
     * @var GigyaApiConnector|\PHPUnit\Framework\MockObject\MockObject
     */
    private $connector;

    public function setUp(): void
    {
        $this->connector = $this->createMock(GigyaApiConnector::class);
        $this->userService = new UserService($this->connector, 'gigya-user-key-test', 'gigya-secret-test');
    }

    public function testGetUserSuccess()
    {
        $userCvsId = 'SP-ACNT200000088042';
        $this->connector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(Response::HTTP_OK, []));
        
        $response = $this->userService->getUserByCvs($userCvsId);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testGetUserError()
    {
        $userCvsId = 'ACNT200000088042';        
        $response = $this->userService->getUserByCvs($userCvsId);
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getCode());
        $this->assertEquals("Bad request", $response->getData());
    }

}
