<?php

namespace App\Controller;

use App\Manager\SDPRManager;
use App\Model\VehicleModel;
use App\Trait\ValidationResponseTrait;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

class UserController extends AbstractController
{
  use ValidationResponseTrait;
  
  #[Route('v1/user/vehicles', name: 'user_vehicles', methods: ['GET'])]

  #[OA\Response(
    response: 200,
    description: 'Success Response',
    content: new JsonContent(type: 'object', properties: [
      new OA\Property(property: 'success', properties: [
        new OA\Property(property: 'vehicles', ref: new Model(type: VehicleModel::class))
      ])
    ])
  )]
  #[OA\Response(
    response: 400,
    description: 'Bad Request',
    content: new JsonContent(type: 'object', properties: [
        new OA\Property(property: 'error', properties: [
          new OA\Property(property: 'message'),
          new OA\Property(property: 'errors', properties: []),
        ])
      ]
    )
  )]

  #[OA\Parameter(
    name: "userId",
    in: "header",
    description: "User id",
    schema: new OA\Schema(type: "string")
  )
  ]
  #[OA\Tag(name: 'User Vehicles')]
  public function getVehicles(Request $request, SDPRManager $spdrManager,ValidatorInterface $validator): JsonResponse
  {
    $userId = $request->headers->get('userId');
    $errors = $validator->validate(
        compact('userId'),
        new Assert\Collection([
            'userId'     => new Assert\NotBlank
        ])
    );
    $messages = $this->getValidationMessages($errors);
    if (!empty($messages)) {
        $response = $this->getValidationErrorResponse($messages)->toArray();
        return $this->json($response['content'], $response['code']);
    }

    $response = $spdrManager->getUserVehicles($userId)->toArray();
    return $this->json($response['content'], $response['code']); 
  }
}