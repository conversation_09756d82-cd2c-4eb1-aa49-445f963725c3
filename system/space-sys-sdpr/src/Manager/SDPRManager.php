<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\SDPRService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

class SDPRManager
{
    use LoggerTrait;
    private SDPRService $service;

    public function __construct(SDPRService $sdprService)
    {
        $this->service = $sdprService;
    }

    /**
     * Get User Vehicles.
     */
    public function getUserVehicles($userId): ResponseArrayFormat
    {
        try {
            $this->logger->info('SDPRManager::getUserVehicles');
            $response = $this->service->getUserVehicles($userId);
            if (Response::HTTP_OK == $response->getCode()) {
                return new SuccessResponse($response->getData());
            }
            if (Response::HTTP_UNAUTHORIZED == $response->getCode()) {
                return new ErrorResponse('Unauthorized', Response::HTTP_UNAUTHORIZED);
            }

            return new ErrorResponse($response->getData(), $response->getCode());

        } catch (\Exception $e) {
            $this->logger->error('SDPRManager::getSDPR User Vehicles: Cached Exception ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
