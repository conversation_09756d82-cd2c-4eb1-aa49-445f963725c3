<?php

namespace App\Service;

use App\Connector\SDPRApiConnector;
use App\Connector\SDPRTokenConnector;
use App\Helper\FileHelper;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;

class SDPRService
{
    use LoggerTrait;
    private string $clientId;
    private string $secret;
    private string $client_requestId;
    private $certifDir;
    private $certName;
    private SDPRApiConnector $sdprConnector;
    private SDPRTokenConnector $tokenConnector;
    private TagAwareCacheAdapter $tagAwareCacheAdapter;
    private FileHelper $fileHelper;

    public function __construct(
        string $clientId,
        string $secret,
        string $client_requestId,
        string $certifDir,
        string $certName,
        SDPRTokenConnector $tokenConnector,
        SDPRApiConnector $sdprConnector,
        FileHelper $fileHelper,
        TagAwareCacheAdapter $tagAwareCacheAdapter
    ) {
        $this->clientId = $clientId;
        $this->secret = $secret;
        $this->client_requestId = $client_requestId;
        $this->certifDir = $certifDir;
        $this->certName = $certName;
        $this->fileHelper = $fileHelper;
        $this->tokenConnector = $tokenConnector;
        $this->sdprConnector = $sdprConnector;
        $this->tagAwareCacheAdapter = $tagAwareCacheAdapter;
        
    }

    public function getSdprToken($userId): mixed
    {
        return $this->tagAwareCacheAdapter->get("SDPR-TOKEN_".$userId, function (ItemInterface $item) {
            $options = [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'body' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->secret
                ],
                'local_cert' => $this->getCertifFile($this->certName, '.pem'),
            ];

        
            $response = $this->tokenConnector->call(Request::METHOD_POST, "/token", $options);

            if (Response::HTTP_OK == $response->getCode()) {
                $responseData = $response->getData();
                if (isset($responseData['access_token'])) {
                    $sdprToken = $responseData['access_token'];
                    $cacheTtl = (int) $responseData['expires_in'] - 30;
                    $item->expiresAfter($cacheTtl);
                }
            }
            return $sdprToken;
        });
    }

    public function getUserVehicles($userId): WSResponse
    {
  
        try {
            $query = [
                'stage' => 'ALL',
                'sdp' => 'ALL',
            ];
            $headers = [
                'Authorization' => 'Bearer ' . $this->getSdprToken($userId),
                'x-originator-type' => 'server',
                'content-type' => 'application/json',
                'clientrequestid' => $this->client_requestId
            ];

            $wsResponse = $this->sdprConnector->call(Request::METHOD_GET, $userId . '/vehicles',
                [
                    'headers' => $headers,
                    'query' => $query,
                    'local_cert' => $this->getCertifFile($this->certName, '.pem'),
                ]
            );


            if (Response::HTTP_OK == $wsResponse->getCode()) {
                $response = $wsResponse->getData();
                if (!empty($response['vehicles'])) {
                    return new WSResponse($wsResponse->getCode(), ['vehicles' => $response['vehicles']]);
                }
                return new WSResponse(Response::HTTP_UNAUTHORIZED, $wsResponse->getData());
            }

            return new WSResponse($wsResponse->getCode(), $wsResponse->getData());

        } catch (\Exception $e) {
            $this->logger->error('Error SDPR Get Vehicles Exception ' . $e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    private function getCertifFile(string $certifName, string $extension): string
    {
        return $this->certifDir . '/' . $this->fileHelper->setExtension($certifName, $extension);
    }
}
